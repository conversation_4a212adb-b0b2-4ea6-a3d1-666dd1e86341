{"name": "dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"typecheck": "tsc --noEmit", "dev": "vite --port=3001", "build": "graphql-codegen && vite build", "serve": "vite preview", "start": "vite", "codegen": "graphql-codegen --watch", "codegen:build": "graphql-codegen"}, "dependencies": {"@auth/core": "0.37.0", "@hookform/resolvers": "^3.10.0", "@internationalized/date": "^3.7.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.66.0", "@tanstack/react-router": "^1.99.0", "@tanstack/react-router-with-query": "^1.104.1", "@tanstack/router-devtools": "^1.99.0", "axios": "^1.8.3", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "emblor": "^1.4.7", "graphql": "^16.8.1", "graphql-request": "^5", "jotai": "^2.12.2", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "next-themes": "^0.4.4", "qrcode": "^1.5.4", "react": "^18.3.1", "react-aria-components": "^1.6.0", "react-day-picker": "^9.5.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "shadcn-dropzone": "^0.2.1", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.2.6", "@graphql-codegen/typescript-react-query": "^6.1.0", "@parcel/watcher": "^2.4.1", "@tanstack/eslint-plugin-router": "^1.99.3", "@tanstack/router-plugin": "^1.99.3", "@types/bcryptjs": "^2.4.6", "@types/lodash": "^4.17.15", "@types/node": "^22.13.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "graphql-codegen-typescript-client": "0.18.2", "graphql-codegen-typescript-common": "0.18.2", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "vite": "^6.0.3"}}