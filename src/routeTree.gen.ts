/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as UsersImport } from './routes/users'
import { Route as LocationsImport } from './routes/locations'
import { Route as InventoryImport } from './routes/inventory'
import { Route as DriveImport } from './routes/drive'
import { Route as AboutImport } from './routes/about'
import { Route as IndexImport } from './routes/index'
import { Route as UsersIndexImport } from './routes/users/index'
import { Route as LocationsIndexImport } from './routes/locations/index'
import { Route as LeavesIndexImport } from './routes/leaves/index'
import { Route as InventoryIndexImport } from './routes/inventory/index'
import { Route as IncidentsMonitoringIndexImport } from './routes/incidents-monitoring/index'
import { Route as HolidaysIndexImport } from './routes/holidays/index'
import { Route as AttendanceIndexImport } from './routes/attendance/index'
import { Route as AnouncementsIndexImport } from './routes/anouncements/index'
import { Route as InventoryLocationInventoryImport } from './routes/inventory/location-inventory'
import { Route as InventoryGuardInventoryImport } from './routes/inventory/guard-inventory'
import { Route as InventoryCreateInventoryImport } from './routes/inventory/create-inventory'
import { Route as DriveSplatImport } from './routes/drive/$'
import { Route as UsersUserIdIndexImport } from './routes/users/$userId/index'
import { Route as LocationsLocationIdIndexImport } from './routes/locations/$locationId/index'
import { Route as LocationsLocationIdShiftImport } from './routes/locations/$locationId/shift'

// Create/Update Routes

const UsersRoute = UsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => rootRoute,
} as any)

const LocationsRoute = LocationsImport.update({
  id: '/locations',
  path: '/locations',
  getParentRoute: () => rootRoute,
} as any)

const InventoryRoute = InventoryImport.update({
  id: '/inventory',
  path: '/inventory',
  getParentRoute: () => rootRoute,
} as any)

const DriveRoute = DriveImport.update({
  id: '/drive',
  path: '/drive',
  getParentRoute: () => rootRoute,
} as any)

const AboutRoute = AboutImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const UsersIndexRoute = UsersIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => UsersRoute,
} as any)

const LocationsIndexRoute = LocationsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => LocationsRoute,
} as any)

const LeavesIndexRoute = LeavesIndexImport.update({
  id: '/leaves/',
  path: '/leaves/',
  getParentRoute: () => rootRoute,
} as any)

const InventoryIndexRoute = InventoryIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => InventoryRoute,
} as any)

const IncidentsMonitoringIndexRoute = IncidentsMonitoringIndexImport.update({
  id: '/incidents-monitoring/',
  path: '/incidents-monitoring/',
  getParentRoute: () => rootRoute,
} as any)

const HolidaysIndexRoute = HolidaysIndexImport.update({
  id: '/holidays/',
  path: '/holidays/',
  getParentRoute: () => rootRoute,
} as any)

const AttendanceIndexRoute = AttendanceIndexImport.update({
  id: '/attendance/',
  path: '/attendance/',
  getParentRoute: () => rootRoute,
} as any)

const AnouncementsIndexRoute = AnouncementsIndexImport.update({
  id: '/anouncements/',
  path: '/anouncements/',
  getParentRoute: () => rootRoute,
} as any)

const InventoryLocationInventoryRoute = InventoryLocationInventoryImport.update(
  {
    id: '/location-inventory',
    path: '/location-inventory',
    getParentRoute: () => InventoryRoute,
  } as any,
)

const InventoryGuardInventoryRoute = InventoryGuardInventoryImport.update({
  id: '/guard-inventory',
  path: '/guard-inventory',
  getParentRoute: () => InventoryRoute,
} as any)

const InventoryCreateInventoryRoute = InventoryCreateInventoryImport.update({
  id: '/create-inventory',
  path: '/create-inventory',
  getParentRoute: () => InventoryRoute,
} as any)

const DriveSplatRoute = DriveSplatImport.update({
  id: '/$',
  path: '/$',
  getParentRoute: () => DriveRoute,
} as any)

const UsersUserIdIndexRoute = UsersUserIdIndexImport.update({
  id: '/$userId/',
  path: '/$userId/',
  getParentRoute: () => UsersRoute,
} as any)

const LocationsLocationIdIndexRoute = LocationsLocationIdIndexImport.update({
  id: '/$locationId/',
  path: '/$locationId/',
  getParentRoute: () => LocationsRoute,
} as any)

const LocationsLocationIdShiftRoute = LocationsLocationIdShiftImport.update({
  id: '/$locationId/shift',
  path: '/$locationId/shift',
  getParentRoute: () => LocationsRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutImport
      parentRoute: typeof rootRoute
    }
    '/drive': {
      id: '/drive'
      path: '/drive'
      fullPath: '/drive'
      preLoaderRoute: typeof DriveImport
      parentRoute: typeof rootRoute
    }
    '/inventory': {
      id: '/inventory'
      path: '/inventory'
      fullPath: '/inventory'
      preLoaderRoute: typeof InventoryImport
      parentRoute: typeof rootRoute
    }
    '/locations': {
      id: '/locations'
      path: '/locations'
      fullPath: '/locations'
      preLoaderRoute: typeof LocationsImport
      parentRoute: typeof rootRoute
    }
    '/users': {
      id: '/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof UsersImport
      parentRoute: typeof rootRoute
    }
    '/drive/$': {
      id: '/drive/$'
      path: '/$'
      fullPath: '/drive/$'
      preLoaderRoute: typeof DriveSplatImport
      parentRoute: typeof DriveImport
    }
    '/inventory/create-inventory': {
      id: '/inventory/create-inventory'
      path: '/create-inventory'
      fullPath: '/inventory/create-inventory'
      preLoaderRoute: typeof InventoryCreateInventoryImport
      parentRoute: typeof InventoryImport
    }
    '/inventory/guard-inventory': {
      id: '/inventory/guard-inventory'
      path: '/guard-inventory'
      fullPath: '/inventory/guard-inventory'
      preLoaderRoute: typeof InventoryGuardInventoryImport
      parentRoute: typeof InventoryImport
    }
    '/inventory/location-inventory': {
      id: '/inventory/location-inventory'
      path: '/location-inventory'
      fullPath: '/inventory/location-inventory'
      preLoaderRoute: typeof InventoryLocationInventoryImport
      parentRoute: typeof InventoryImport
    }
    '/anouncements/': {
      id: '/anouncements/'
      path: '/anouncements'
      fullPath: '/anouncements'
      preLoaderRoute: typeof AnouncementsIndexImport
      parentRoute: typeof rootRoute
    }
    '/attendance/': {
      id: '/attendance/'
      path: '/attendance'
      fullPath: '/attendance'
      preLoaderRoute: typeof AttendanceIndexImport
      parentRoute: typeof rootRoute
    }
    '/holidays/': {
      id: '/holidays/'
      path: '/holidays'
      fullPath: '/holidays'
      preLoaderRoute: typeof HolidaysIndexImport
      parentRoute: typeof rootRoute
    }
    '/incidents-monitoring/': {
      id: '/incidents-monitoring/'
      path: '/incidents-monitoring'
      fullPath: '/incidents-monitoring'
      preLoaderRoute: typeof IncidentsMonitoringIndexImport
      parentRoute: typeof rootRoute
    }
    '/inventory/': {
      id: '/inventory/'
      path: '/'
      fullPath: '/inventory/'
      preLoaderRoute: typeof InventoryIndexImport
      parentRoute: typeof InventoryImport
    }
    '/leaves/': {
      id: '/leaves/'
      path: '/leaves'
      fullPath: '/leaves'
      preLoaderRoute: typeof LeavesIndexImport
      parentRoute: typeof rootRoute
    }
    '/locations/': {
      id: '/locations/'
      path: '/'
      fullPath: '/locations/'
      preLoaderRoute: typeof LocationsIndexImport
      parentRoute: typeof LocationsImport
    }
    '/users/': {
      id: '/users/'
      path: '/'
      fullPath: '/users/'
      preLoaderRoute: typeof UsersIndexImport
      parentRoute: typeof UsersImport
    }
    '/locations/$locationId/shift': {
      id: '/locations/$locationId/shift'
      path: '/$locationId/shift'
      fullPath: '/locations/$locationId/shift'
      preLoaderRoute: typeof LocationsLocationIdShiftImport
      parentRoute: typeof LocationsImport
    }
    '/locations/$locationId/': {
      id: '/locations/$locationId/'
      path: '/$locationId'
      fullPath: '/locations/$locationId'
      preLoaderRoute: typeof LocationsLocationIdIndexImport
      parentRoute: typeof LocationsImport
    }
    '/users/$userId/': {
      id: '/users/$userId/'
      path: '/$userId'
      fullPath: '/users/$userId'
      preLoaderRoute: typeof UsersUserIdIndexImport
      parentRoute: typeof UsersImport
    }
  }
}

// Create and export the route tree

interface DriveRouteChildren {
  DriveSplatRoute: typeof DriveSplatRoute
}

const DriveRouteChildren: DriveRouteChildren = {
  DriveSplatRoute: DriveSplatRoute,
}

const DriveRouteWithChildren = DriveRoute._addFileChildren(DriveRouteChildren)

interface InventoryRouteChildren {
  InventoryCreateInventoryRoute: typeof InventoryCreateInventoryRoute
  InventoryGuardInventoryRoute: typeof InventoryGuardInventoryRoute
  InventoryLocationInventoryRoute: typeof InventoryLocationInventoryRoute
  InventoryIndexRoute: typeof InventoryIndexRoute
}

const InventoryRouteChildren: InventoryRouteChildren = {
  InventoryCreateInventoryRoute: InventoryCreateInventoryRoute,
  InventoryGuardInventoryRoute: InventoryGuardInventoryRoute,
  InventoryLocationInventoryRoute: InventoryLocationInventoryRoute,
  InventoryIndexRoute: InventoryIndexRoute,
}

const InventoryRouteWithChildren = InventoryRoute._addFileChildren(
  InventoryRouteChildren,
)

interface LocationsRouteChildren {
  LocationsIndexRoute: typeof LocationsIndexRoute
  LocationsLocationIdShiftRoute: typeof LocationsLocationIdShiftRoute
  LocationsLocationIdIndexRoute: typeof LocationsLocationIdIndexRoute
}

const LocationsRouteChildren: LocationsRouteChildren = {
  LocationsIndexRoute: LocationsIndexRoute,
  LocationsLocationIdShiftRoute: LocationsLocationIdShiftRoute,
  LocationsLocationIdIndexRoute: LocationsLocationIdIndexRoute,
}

const LocationsRouteWithChildren = LocationsRoute._addFileChildren(
  LocationsRouteChildren,
)

interface UsersRouteChildren {
  UsersIndexRoute: typeof UsersIndexRoute
  UsersUserIdIndexRoute: typeof UsersUserIdIndexRoute
}

const UsersRouteChildren: UsersRouteChildren = {
  UsersIndexRoute: UsersIndexRoute,
  UsersUserIdIndexRoute: UsersUserIdIndexRoute,
}

const UsersRouteWithChildren = UsersRoute._addFileChildren(UsersRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/drive': typeof DriveRouteWithChildren
  '/inventory': typeof InventoryRouteWithChildren
  '/locations': typeof LocationsRouteWithChildren
  '/users': typeof UsersRouteWithChildren
  '/drive/$': typeof DriveSplatRoute
  '/inventory/create-inventory': typeof InventoryCreateInventoryRoute
  '/inventory/guard-inventory': typeof InventoryGuardInventoryRoute
  '/inventory/location-inventory': typeof InventoryLocationInventoryRoute
  '/anouncements': typeof AnouncementsIndexRoute
  '/attendance': typeof AttendanceIndexRoute
  '/holidays': typeof HolidaysIndexRoute
  '/incidents-monitoring': typeof IncidentsMonitoringIndexRoute
  '/inventory/': typeof InventoryIndexRoute
  '/leaves': typeof LeavesIndexRoute
  '/locations/': typeof LocationsIndexRoute
  '/users/': typeof UsersIndexRoute
  '/locations/$locationId/shift': typeof LocationsLocationIdShiftRoute
  '/locations/$locationId': typeof LocationsLocationIdIndexRoute
  '/users/$userId': typeof UsersUserIdIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/drive': typeof DriveRouteWithChildren
  '/drive/$': typeof DriveSplatRoute
  '/inventory/create-inventory': typeof InventoryCreateInventoryRoute
  '/inventory/guard-inventory': typeof InventoryGuardInventoryRoute
  '/inventory/location-inventory': typeof InventoryLocationInventoryRoute
  '/anouncements': typeof AnouncementsIndexRoute
  '/attendance': typeof AttendanceIndexRoute
  '/holidays': typeof HolidaysIndexRoute
  '/incidents-monitoring': typeof IncidentsMonitoringIndexRoute
  '/inventory': typeof InventoryIndexRoute
  '/leaves': typeof LeavesIndexRoute
  '/locations': typeof LocationsIndexRoute
  '/users': typeof UsersIndexRoute
  '/locations/$locationId/shift': typeof LocationsLocationIdShiftRoute
  '/locations/$locationId': typeof LocationsLocationIdIndexRoute
  '/users/$userId': typeof UsersUserIdIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/drive': typeof DriveRouteWithChildren
  '/inventory': typeof InventoryRouteWithChildren
  '/locations': typeof LocationsRouteWithChildren
  '/users': typeof UsersRouteWithChildren
  '/drive/$': typeof DriveSplatRoute
  '/inventory/create-inventory': typeof InventoryCreateInventoryRoute
  '/inventory/guard-inventory': typeof InventoryGuardInventoryRoute
  '/inventory/location-inventory': typeof InventoryLocationInventoryRoute
  '/anouncements/': typeof AnouncementsIndexRoute
  '/attendance/': typeof AttendanceIndexRoute
  '/holidays/': typeof HolidaysIndexRoute
  '/incidents-monitoring/': typeof IncidentsMonitoringIndexRoute
  '/inventory/': typeof InventoryIndexRoute
  '/leaves/': typeof LeavesIndexRoute
  '/locations/': typeof LocationsIndexRoute
  '/users/': typeof UsersIndexRoute
  '/locations/$locationId/shift': typeof LocationsLocationIdShiftRoute
  '/locations/$locationId/': typeof LocationsLocationIdIndexRoute
  '/users/$userId/': typeof UsersUserIdIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/drive'
    | '/inventory'
    | '/locations'
    | '/users'
    | '/drive/$'
    | '/inventory/create-inventory'
    | '/inventory/guard-inventory'
    | '/inventory/location-inventory'
    | '/anouncements'
    | '/attendance'
    | '/holidays'
    | '/incidents-monitoring'
    | '/inventory/'
    | '/leaves'
    | '/locations/'
    | '/users/'
    | '/locations/$locationId/shift'
    | '/locations/$locationId'
    | '/users/$userId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/drive'
    | '/drive/$'
    | '/inventory/create-inventory'
    | '/inventory/guard-inventory'
    | '/inventory/location-inventory'
    | '/anouncements'
    | '/attendance'
    | '/holidays'
    | '/incidents-monitoring'
    | '/inventory'
    | '/leaves'
    | '/locations'
    | '/users'
    | '/locations/$locationId/shift'
    | '/locations/$locationId'
    | '/users/$userId'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/drive'
    | '/inventory'
    | '/locations'
    | '/users'
    | '/drive/$'
    | '/inventory/create-inventory'
    | '/inventory/guard-inventory'
    | '/inventory/location-inventory'
    | '/anouncements/'
    | '/attendance/'
    | '/holidays/'
    | '/incidents-monitoring/'
    | '/inventory/'
    | '/leaves/'
    | '/locations/'
    | '/users/'
    | '/locations/$locationId/shift'
    | '/locations/$locationId/'
    | '/users/$userId/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  DriveRoute: typeof DriveRouteWithChildren
  InventoryRoute: typeof InventoryRouteWithChildren
  LocationsRoute: typeof LocationsRouteWithChildren
  UsersRoute: typeof UsersRouteWithChildren
  AnouncementsIndexRoute: typeof AnouncementsIndexRoute
  AttendanceIndexRoute: typeof AttendanceIndexRoute
  HolidaysIndexRoute: typeof HolidaysIndexRoute
  IncidentsMonitoringIndexRoute: typeof IncidentsMonitoringIndexRoute
  LeavesIndexRoute: typeof LeavesIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  DriveRoute: DriveRouteWithChildren,
  InventoryRoute: InventoryRouteWithChildren,
  LocationsRoute: LocationsRouteWithChildren,
  UsersRoute: UsersRouteWithChildren,
  AnouncementsIndexRoute: AnouncementsIndexRoute,
  AttendanceIndexRoute: AttendanceIndexRoute,
  HolidaysIndexRoute: HolidaysIndexRoute,
  IncidentsMonitoringIndexRoute: IncidentsMonitoringIndexRoute,
  LeavesIndexRoute: LeavesIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/about",
        "/drive",
        "/inventory",
        "/locations",
        "/users",
        "/anouncements/",
        "/attendance/",
        "/holidays/",
        "/incidents-monitoring/",
        "/leaves/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/about": {
      "filePath": "about.tsx"
    },
    "/drive": {
      "filePath": "drive.tsx",
      "children": [
        "/drive/$"
      ]
    },
    "/inventory": {
      "filePath": "inventory.tsx",
      "children": [
        "/inventory/create-inventory",
        "/inventory/guard-inventory",
        "/inventory/location-inventory",
        "/inventory/"
      ]
    },
    "/locations": {
      "filePath": "locations.tsx",
      "children": [
        "/locations/",
        "/locations/$locationId/shift",
        "/locations/$locationId/"
      ]
    },
    "/users": {
      "filePath": "users.tsx",
      "children": [
        "/users/",
        "/users/$userId/"
      ]
    },
    "/drive/$": {
      "filePath": "drive/$.tsx",
      "parent": "/drive"
    },
    "/inventory/create-inventory": {
      "filePath": "inventory/create-inventory.tsx",
      "parent": "/inventory"
    },
    "/inventory/guard-inventory": {
      "filePath": "inventory/guard-inventory.tsx",
      "parent": "/inventory"
    },
    "/inventory/location-inventory": {
      "filePath": "inventory/location-inventory.tsx",
      "parent": "/inventory"
    },
    "/anouncements/": {
      "filePath": "anouncements/index.tsx"
    },
    "/attendance/": {
      "filePath": "attendance/index.tsx"
    },
    "/holidays/": {
      "filePath": "holidays/index.tsx"
    },
    "/incidents-monitoring/": {
      "filePath": "incidents-monitoring/index.tsx"
    },
    "/inventory/": {
      "filePath": "inventory/index.tsx",
      "parent": "/inventory"
    },
    "/leaves/": {
      "filePath": "leaves/index.tsx"
    },
    "/locations/": {
      "filePath": "locations/index.tsx",
      "parent": "/locations"
    },
    "/users/": {
      "filePath": "users/index.tsx",
      "parent": "/users"
    },
    "/locations/$locationId/shift": {
      "filePath": "locations/$locationId/shift.tsx",
      "parent": "/locations"
    },
    "/locations/$locationId/": {
      "filePath": "locations/$locationId/index.tsx",
      "parent": "/locations"
    },
    "/users/$userId/": {
      "filePath": "users/$userId/index.tsx",
      "parent": "/users"
    }
  }
}
ROUTE_MANIFEST_END */
