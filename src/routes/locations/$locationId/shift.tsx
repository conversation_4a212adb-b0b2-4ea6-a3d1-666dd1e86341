import ShiftCalendar from '@/components/shift-calendar';
import ShiftHeader from '@/components/shifts/ShiftHeader';
import ShiftsList from '@/components/shifts/ShiftsList';
import { Shift, useShiftQuery } from '@/generated/graphql';
import { createFileRoute } from '@tanstack/react-router';
import { endOfDay, startOfDay } from 'date-fns';
import { useMemo } from 'react';
import { z } from 'zod';

export const Route = createFileRoute('/locations/$locationId/shift')({
  component: RouteComponent,
  validateSearch: z.object({
    date: z.coerce.date().default(new Date()),
    shiftId: z.string().optional()
  }),
  loaderDeps: d => d,
  loader({ route }) {
    return { crumb: 'Shift' };
  }
});

function RouteComponent() {
  // const name = Route.parentRoute?.useLoaderData()?.crumb

  const search = Route.useSearch();
  const date = useMemo(() => new Date(search.date), [search.date]);

  const params = Route.useParams();
  const nav = Route.useNavigate();

  const { shiftId } = search;
  const setShiftId = (shiftId: string) => {
    nav({ search: { shiftId, date: search.date } });
  };

  const setDate = (date: Date) => nav({ search: { date } });

  const { data: shifts, isLoading } = useShiftQuery(
    {
      input: {
        locationId: params.locationId,
        startDateTime: startOfDay(date),
        endDateTime: endOfDay(date)
      }
    },
    { initialData: { shifts: [] } }
  );

  if (isLoading) return null;
  return (
    <div>
      <div className="rounded-t-xl border border-green-800/20 bg-secondary p-4">
        {/* header */}
        <ShiftHeader
          date={date}
          locationId={params.locationId}
          setDate={setDate}
        />
      </div>
      <div className="flex h-[400px] rounded-b-xl border-x border-b border-green-800/20 p-4">
        {/* Shifts */}
        <ShiftsList
          shiftId={shiftId}
          shifts={shifts?.shifts as Shift[]}
          locationId={params.locationId}
          setShiftId={setShiftId}
          date={date}
        />
        <ShiftCalendar
          data={{}}
          date={date}
          setDate={date => {
            if (date) setDate(date);
          }}
        />
      </div>
    </div>
  );
}
