import {
  dateTimeSchema,
  FormAppointmentPicker
} from '@/components/appointment-picker';
import FormMultiSelect, {
  multiSelectSchema
} from '@/components/forms/FormMultiSelect';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { Clock, Plus } from 'lucide-react';
import { FormProvider, useForm, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import Form<PERSON>heckBox from '@/components/forms/FormCheckBox';
import { errorHandler } from '@/lib/utils';
import { toast } from 'sonner';
import { endOfDay, format, startOfDay } from 'date-fns';
import { useEffect } from 'react';
import { UserRoles, useUserQuery, useUsersQuery } from '@/generated/graphql';

const schema = z.object({
  shiftId: z.string(),
  userIds: multiSelectSchema.min(1),
  updateRecurring: z.boolean().optional()
});

type Form = z.infer<typeof schema>;

export const useUpdateShiftFormMethods = (onSuccess?: () => void) => {
  const methods = useForm<Form>({
    defaultValues: {
      userIds: [],
      shiftId: '',
      updateRecurring: true
    },
    resolver: zodResolver(schema)
  });

  const updateShifts = () => {};

  const onSubmit = async (data: Form) => {
    try {
      toast.success('Shift(s) Updated successfully');
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return { onSubmit, methods };
};

export default function UpdateShiftForm({
  methods,
  onSubmit
}: {
  methods: UseFormReturn<Form>;
  onSubmit: (data: Form) => Promise<void>;
}) {
  const { data: securityGuards } = useUsersQuery(
    {
      input: {
        roles: [
          UserRoles.LocalGuard,
          UserRoles.NepalGuard,
          UserRoles.BufferGuard
        ]
      }
    },
    { initialData: { users: [] } }
  );

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormMultiSelect
          name="userIds"
          label="Guards"
          placeholder="Select guards"
          options={(securityGuards?.users ?? []).map(s => ({
            value: s.id,
            label: s.fullname
          }))}
        />
        <Separator />
        <FormCheckBox name="updateRecurring" label="Update Recurring Shifts" />
        <Button className="w-full" effect="shineHover">
          Update Shift
        </Button>
      </form>
    </FormProvider>
  );
}
