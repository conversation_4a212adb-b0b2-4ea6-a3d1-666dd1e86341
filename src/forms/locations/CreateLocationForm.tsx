import FormFile, { useFormFile } from '@/components/forms/FormFile';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { errorHandler } from '@/lib/utils';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import { Button } from '@/components/ui/button';
import FormPhoneInput from '@/components/forms/FormPhoneInput';
import FormTextArea from '@/components/forms/FormTextArea';
import { fetchData } from '@/client';
import {
  StorageSystemFoldersQuery,
  StorageSystemFoldersQueryVariables,
  useCreateLocationMutation,
  useStorageSystemFoldersQuery
} from '@/generated/graphql';

const schema = z.object({
  name: z.string().nonempty('Name is required'),
  description: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional()
});

type CreateLocationForm = z.infer<typeof schema>;

export const useCreateLocationForm = () => {
  const methods = useForm<CreateLocationForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
      address: '',
      emergencyContact: ''
    }
  });

  return { methods };
};

export default function CreateLocationForm() {
  const { methods } = useCreateLocationForm();
  const fileMethods = useFormFile();
  const { mutateAsync: createLocation } = useCreateLocationMutation();

  const onSubmit = async (data: CreateLocationForm) => {
    toast.promise(createLocation({ createLocationInput: data }), {
      loading: 'Creating location...',
      success: 'Location created successfully',
      error: 'Failed to create location'
    });
    methods.reset();
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormField name="name" label="Name" placeholder="Enter location name" />
        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter location description"
        />
        <FormTextArea
          name="address"
          label="Address"
          placeholder="Enter location address"
          className="min-h-24"
        />
        <FormPhoneInput
          name="emergencyContact"
          label="Emergency Contact"
          placeholder="Emergency contact"
        />

        <Button type="submit">Create Location</Button>
      </form>
    </FormProvider>
  );
}
