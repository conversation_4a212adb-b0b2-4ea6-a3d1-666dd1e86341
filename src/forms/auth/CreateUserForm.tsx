import { FormComponentProps } from '@/@types';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import FormPhoneInput from '@/components/forms/FormPhoneInput';
import FormSelect from '@/components/forms/FormSelect';
import { Button } from '@/components/ui/button';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import {
  useCreateUserMutation,
  UserRoles,
  UserStatus,
  useUsersQuery
} from '@/generated/graphql';
import { enumToOptions } from '@/lib/utils';
import { queryClient } from '@/main';
import { zodResolver } from '@hookform/resolvers/zod';

import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

/**Form Schema */
const schema = z.object({
  phone: z.string().nonempty('Phone number must be at least 10 digits'),
  password: z.string().min(4, 'Password must be at least 4 characters'),
  fullname: z.string().nonempty('Please enter full name'),
  userStatus: z.nativeEnum(UserStatus, {
    message: 'Please select a status'
  }),
  role: z.nativeEnum(UserRoles, {
    message: 'Please select a role'
  })
});

/**Form Types */
type CreateUserForm = z.infer<typeof schema>;

/**Form Methods */
export const useCreateUserFormMethods = () => {
  const methods = useForm<CreateUserForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      password: '',
      phone: '',
      fullname: '',
      role: UserRoles.LocalGuard,
      userStatus: UserStatus.Active
    }
  });

  const { mutateAsync: createUser } = useCreateUserMutation();

  const onSubmit = async ({
    role,
    userStatus: status,
    ...data
  }: CreateUserForm) => {
    try {
      toast.promise(
        createUser({ input: { role, userStatus: status, ...data } }),
        {
          loading: 'Creating user...',
          success: () => {
            queryClient.invalidateQueries({
              queryKey: useUsersQuery.getKey({ input: {} })
            });
            return 'User created successfully';
          },
          error: 'Failed to create user'
        }
      );
    } catch (ex) {}
  };

  return { onSubmit, methods };
};

/**Form Component */
export default function CreateUserForm({
  methods,
  onSubmit
}: FormComponentProps<CreateUserForm>) {
  return (
    <FormProvider {...methods}>
      <form className="space-y-4" onSubmit={methods.handleSubmit(onSubmit)}>
        <FormField
          name="fullname"
          placeholder="Enter full name"
          label="Full Name"
        />
        <div className="grid grid-cols-2 gap-4">
          <FormPhoneInput
            name="phone"
            placeholder="Enter phone number"
            label="Phone"
          />

          <FormSelect name="role" label="Role" placeholder="Select a role">
            <SelectGroup>
              <SelectLabel>Select Role</SelectLabel>
              {enumToOptions(UserRoles).map(({ value, label }) => (
                <SelectItem value={value} key={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectGroup>
          </FormSelect>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            name="password"
            placeholder="Enter password"
            label="Password"
            type="password"
          />

          <FormSelect
            name="userStatus"
            label="Status"
            placeholder="Select user status"
          >
            <SelectGroup>
              <SelectLabel>Select status</SelectLabel>
              {enumToOptions(UserStatus).map(({ value, label }) => (
                <SelectItem value={value} key={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectGroup>
          </FormSelect>
        </div>

        <Button className="w-full">Create User</Button>
      </form>
    </FormProvider>
  );
}
