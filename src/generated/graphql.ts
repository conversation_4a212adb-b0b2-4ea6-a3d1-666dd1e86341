import { useQuery, useMutation, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { fetchData } from '@/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: Date; output: Date; }
};

/** User/Employee allowances */
export type Allowance = {
  __typename?: 'Allowance';
  /** Amount of allowance */
  amount: Scalars['Float']['output'];
  /** Description of allowance */
  description?: Maybe<Scalars['String']['output']>;
  /** location allowance */
  location?: Maybe<Location>;
  /** allowance receipt */
  receipt?: Maybe<Scalars['String']['output']>;
  /** Status of allowance */
  status?: Maybe<AllowanceStatus>;
  /** Type of allowance */
  type: Scalars['String']['output'];
  /** allowance created by */
  user: User;
};

export type AllowanceClaim = {
  __typename?: 'AllowanceClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  from: Scalars['DateTime']['output'];
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts?: Maybe<Array<Scalars['String']['output']>>;
  to: Scalars['DateTime']['output'];
  /** Claim created by */
  user: User;
  workingHours: Scalars['Int']['output'];
};

export enum AllowanceStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type AnalyticsFilterInput = {
  dateRange?: InputMaybe<DateRangeInput>;
  locationId?: InputMaybe<Scalars['String']['input']>;
  userRole?: InputMaybe<UserRoles>;
};

export type Anouncement = {
  __typename?: 'Anouncement';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  document?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  userRoles?: Maybe<Array<UserRoles>>;
  users?: Maybe<Array<User>>;
};

/** User attendance */
export type Attendance = {
  __typename?: 'Attendance';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  endTime?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  location?: Maybe<Location>;
  overTime?: Maybe<Scalars['DateTime']['output']>;
  overTimeSpentInMinutes: Scalars['Int']['output'];
  shift?: Maybe<Shift>;
  startTime: Scalars['DateTime']['output'];
  timeSpentInMinutes: Scalars['Int']['output'];
  updatedAt: Scalars['DateTime']['output'];
  user?: Maybe<User>;
};

export type AttendanceInput = {
  shiftId?: InputMaybe<Scalars['String']['input']>;
};

export type AttributeType = {
  __typename?: 'AttributeType';
  attibuteName: Scalars['String']['output'];
  attributeValues: Array<Scalars['String']['output']>;
};

export type AttributeTypeInput = {
  attibuteName: Scalars['String']['input'];
  attributeValues: Array<Scalars['String']['input']>;
};

export type AuthOutput = {
  __typename?: 'AuthOutput';
  /** Access token */
  access_token: Scalars['String']['output'];
};

export type Checkpoint = {
  __typename?: 'Checkpoint';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  isDeleted: Scalars['Boolean']['output'];
  location: Location;
  locationCoordinates?: Maybe<Array<Scalars['Float']['output']>>;
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type CheckpointAttendance = {
  __typename?: 'CheckpointAttendance';
  _id: Scalars['ID']['output'];
  checkpoint: Checkpoint;
  checkpointData: CheckpointData;
  createdAt: Scalars['DateTime']['output'];
  guard: User;
  id: Scalars['ID']['output'];
  location: Location;
  scannedAt: Scalars['DateTime']['output'];
  scannedLocation?: Maybe<Array<Scalars['Float']['output']>>;
  updatedAt: Scalars['DateTime']['output'];
};

export type CheckpointData = {
  __typename?: 'CheckpointData';
  locationCoordinates?: Maybe<Array<Scalars['Float']['output']>>;
  name: Scalars['String']['output'];
};

export type ClaimInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  claimType: ClaimType;
  client?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  distance?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['DateTime']['input']>;
  items?: InputMaybe<Array<Scalars['String']['input']>>;
  purpose?: InputMaybe<Scalars['String']['input']>;
  receipts?: InputMaybe<Array<Scalars['String']['input']>>;
  site?: InputMaybe<Scalars['String']['input']>;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  toll?: InputMaybe<Scalars['String']['input']>;
  user: Scalars['String']['input'];
  workingHours?: InputMaybe<Scalars['Int']['input']>;
};

export enum ClaimStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum ClaimType {
  Allowance = 'ALLOWANCE',
  Expense = 'EXPENSE',
  Site = 'SITE',
  Travel = 'TRAVEL'
}

export type ClaimUnion = AllowanceClaim | ExpenseClaim | SiteClaim | TravelClaim;

export type Claims = {
  __typename?: 'Claims';
  _id: Scalars['ID']['output'];
  /** claim data in calims */
  claimData: ClaimUnion;
  /** Type of the claim */
  claimType: ClaimType;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** Claim processed by */
  processedBy?: Maybe<User>;
  rejectedReason?: Maybe<Scalars['String']['output']>;
  /** Claim status */
  status?: Maybe<ClaimStatus>;
  updatedAt: Scalars['DateTime']['output'];
};

export type ClearFaceInput = {
  userId: Scalars['String']['input'];
};

export type ClockInInput = {
  base64Img: Scalars['String']['input'];
  date: Scalars['DateTime']['input'];
  locationId: Scalars['String']['input'];
  shiftId: Scalars['String']['input'];
};

export type ClockOutInput = {
  attendanceId: Scalars['String']['input'];
  base64Img: Scalars['String']['input'];
};

export type Contact = {
  __typename?: 'Contact';
  countryCode: Scalars['String']['output'];
  phone: Scalars['String']['output'];
};

export type ContactInput = {
  countryCode: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreateAllowanceInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreateAnouncementInput = {
  date: Scalars['DateTime']['input'];
  description: Scalars['String']['input'];
  document?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  userRoles?: InputMaybe<Array<UserRoles>>;
  users?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CreateAttendanceInput = {
  date: Scalars['DateTime']['input'];
  endTime: Scalars['DateTime']['input'];
  locationId: Scalars['String']['input'];
  overTime: Scalars['DateTime']['input'];
  shiftId: Scalars['String']['input'];
  startTime: Scalars['DateTime']['input'];
  userId: Scalars['String']['input'];
};

export type CreateCheckpointAttendanceInput = {
  checkpointId: Scalars['String']['input'];
  locationId: Scalars['String']['input'];
  scannedLocation?: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type CreateCheckpointInput = {
  location: Scalars['String']['input'];
  locationCoordinates?: InputMaybe<Array<Scalars['Float']['input']>>;
  name: Scalars['String']['input'];
};

export type CreateFolderDto = {
  folder: Scalars['String']['input'];
  parentFolderId?: InputMaybe<Scalars['String']['input']>;
  path: Scalars['String']['input'];
};

export type CreateHolidayInput = {
  /** date of the holiday */
  date: Scalars['DateTime']['input'];
  /** description of the holiday */
  description: Scalars['String']['input'];
  /** name of the holiday */
  name: Scalars['String']['input'];
};

export type CreateIncidentInput = {
  description: Scalars['String']['input'];
  evidence: Array<EvidenceInput>;
  location: Scalars['String']['input'];
  priorityLevel: PriorityLevel;
};

export type CreateInventoryInput = {
  attributes?: InputMaybe<Array<AttributeTypeInput>>;
  description?: InputMaybe<Scalars['String']['input']>;
  /** Inventory item name */
  item: Scalars['String']['input'];
  items?: InputMaybe<Array<InventoryItemInputType>>;
  type: InventoryType;
};

export type CreateInventoryRequestInput = {
  inventory: Scalars['String']['input'];
  items: Array<RequestedItemInput>;
  requestedBy: Scalars['String']['input'];
};

export type CreateLeaveInput = {
  endDateTime: Scalars['DateTime']['input'];
  leaveType: LeaveType;
  reason: Scalars['String']['input'];
  startDateTime: Scalars['DateTime']['input'];
  user: Scalars['String']['input'];
};

export type CreateLocationInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  emergencyContact?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type CreatePaymentConfigInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreatePaymentCorrectionInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreateShiftInput = {
  endDateTime: Scalars['DateTime']['input'];
  isRecurring?: InputMaybe<Scalars['Boolean']['input']>;
  locationId: Scalars['ID']['input'];
  overTime?: InputMaybe<Scalars['DateTime']['input']>;
  recurringId?: InputMaybe<Scalars['String']['input']>;
  startDateTime: Scalars['DateTime']['input'];
  userIds: Array<Scalars['ID']['input']>;
};

export type CreateTaskInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreateUserDocumentInput = {
  documentName: Scalars['String']['input'];
  url: Scalars['String']['input'];
  user: Scalars['String']['input'];
};

export type CreateUserInput = {
  /** user fullname */
  fullname: Scalars['String']['input'];
  /** user password */
  password: Scalars['String']['input'];
  /** user phone number */
  phone: Scalars['String']['input'];
  /** user role */
  role: UserRoles;
  /** user active status */
  userStatus: UserStatus;
};

export type CreateUserProfileInput = {
  ID?: InputMaybe<Scalars['String']['input']>;
  bankAccNumber?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  currentAddress?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  emergencyContact?: InputMaybe<Array<EmergencyContactInput>>;
  gender?: InputMaybe<Scalars['String']['input']>;
  ic?: InputMaybe<Scalars['String']['input']>;
  joinedAt?: InputMaybe<Scalars['DateTime']['input']>;
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  passport?: InputMaybe<Scalars['String']['input']>;
  passportExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitNumber?: InputMaybe<Scalars['String']['input']>;
  placeOfBirth?: InputMaybe<Scalars['String']['input']>;
  user: Scalars['String']['input'];
};

export type DateRangeInput = {
  endDate: Scalars['DateTime']['input'];
  startDate: Scalars['DateTime']['input'];
};

export type EmergencyContact = {
  __typename?: 'EmergencyContact';
  contact: Contact;
  name: Scalars['String']['output'];
  relation: Scalars['String']['output'];
};

export type EmergencyContactInput = {
  contact: ContactInput;
  name: Scalars['String']['input'];
  relation: Scalars['String']['input'];
};

export type Evidence = {
  __typename?: 'Evidence';
  type: EvidenceType;
  url: Scalars['String']['output'];
};

export type EvidenceInput = {
  type: EvidenceType;
  url: Scalars['String']['input'];
};

export enum EvidenceType {
  Document = 'DOCUMENT',
  Image = 'IMAGE',
  Video = 'VIDEO'
}

export type ExpenseClaim = {
  __typename?: 'ExpenseClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  date: Scalars['DateTime']['output'];
  /** List of items */
  items: Array<Scalars['String']['output']>;
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
  /** Claim created by */
  user: User;
};

export type FaceInformation = {
  __typename?: 'FaceInformation';
  faceId?: Maybe<Scalars['String']['output']>;
};

export type FaceInformationInput = {
  faceId?: InputMaybe<Scalars['String']['input']>;
};

export type FindCheckpointsInput = {
  location?: InputMaybe<Scalars['String']['input']>;
};

export type FindClaimsInput = {
  claimType?: InputMaybe<ClaimType>;
  status?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type FindInventoryRequestsInput = {
  inventoryType?: InputMaybe<InventoryType>;
  requestedBy?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<RequestStatus>;
};

export type FindLeavesInput = {
  leaveStatus?: InputMaybe<Scalars['String']['input']>;
  leaveType?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type GuardActivityStats = {
  __typename?: 'GuardActivityStats';
  averageTimeSpent: Scalars['Float']['output'];
  totalAttendance: Scalars['Int']['output'];
  totalCheckpoints: Scalars['Int']['output'];
  totalIncidents: Scalars['Int']['output'];
};

/** The health of the server */
export type Health = {
  __typename?: 'Health';
  status: Scalars['Boolean']['output'];
};

export type Holiday = {
  __typename?: 'Holiday';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type Incident = {
  __typename?: 'Incident';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  evidence: Array<Evidence>;
  id: Scalars['ID']['output'];
  location: Location;
  priorityLevel: PriorityLevel;
  reportedAt: Scalars['DateTime']['output'];
  reportedBy: User;
  updatedAt: Scalars['DateTime']['output'];
};

export type IndexFaceInput = {
  base64Img: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type Inventory = {
  __typename?: 'Inventory';
  _id: Scalars['ID']['output'];
  attributes?: Maybe<Array<AttributeType>>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Inventory item name */
  item: Scalars['String']['output'];
  items?: Maybe<Array<InventoryItem>>;
  type: InventoryType;
  updatedAt: Scalars['DateTime']['output'];
};

export type InventoryInput = {
  inventoryType: InventoryType;
};

export type InventoryItem = {
  __typename?: 'InventoryItem';
  costPrice: Scalars['Float']['output'];
  item: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  sellingPrice: Scalars['Float']['output'];
  sku: Scalars['String']['output'];
};

export type InventoryItemInputType = {
  costPrice: Scalars['Float']['input'];
  item: Scalars['String']['input'];
  quantity: Scalars['Int']['input'];
  sellingPrice: Scalars['Float']['input'];
  sku: Scalars['String']['input'];
};

export type InventoryRequest = {
  __typename?: 'InventoryRequest';
  _id: Scalars['ID']['output'];
  acceptedAt?: Maybe<Scalars['DateTime']['output']>;
  acceptedBy?: Maybe<User>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  inventory: Inventory;
  inventoryType: InventoryType;
  items: Array<RequestedItem>;
  requestedBy: User;
  status: RequestStatus;
  updatedAt: Scalars['DateTime']['output'];
};

export type InventoryStats = {
  __typename?: 'InventoryStats';
  requestsPending: Scalars['Int']['output'];
  totalItems: Scalars['Int']['output'];
  type: Scalars['String']['output'];
};

export enum InventoryType {
  Guard = 'GUARD',
  Location = 'LOCATION'
}

export type Leave = {
  __typename?: 'Leave';
  _id: Scalars['ID']['output'];
  approvedBy?: Maybe<User>;
  createdAt: Scalars['DateTime']['output'];
  endDateTime: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  leaveStatus?: Maybe<LeaveStatus>;
  leaveType: LeaveType;
  reason: Scalars['String']['output'];
  rejectedReason?: Maybe<Scalars['String']['output']>;
  startDateTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
  user?: Maybe<User>;
};

export type LeaveStats = {
  __typename?: 'LeaveStats';
  approved: Scalars['Int']['output'];
  pending: Scalars['Int']['output'];
  rejected: Scalars['Int']['output'];
};

export enum LeaveStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum LeaveType {
  Fullday = 'FULLDAY',
  Halfday = 'HALFDAY'
}

export type Location = {
  __typename?: 'Location';
  _id: Scalars['ID']['output'];
  address?: Maybe<Scalars['String']['output']>;
  checkpoints?: Maybe<Array<Checkpoint>>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  emergencyContact?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  clearFace: Scalars['Boolean']['output'];
  clockIn: Attendance;
  clockOut: Attendance;
  createAllowance: Allowance;
  createAnouncement: Anouncement;
  createAttendance: Attendance;
  createCheckpoint: Checkpoint;
  createCheckpointAttendance: CheckpointAttendance;
  createClaim: Claims;
  createFolder: Storage;
  createHoliday: Holiday;
  createIncident: Incident;
  createInventory: Inventory;
  createInventoryRequest: InventoryRequest;
  createLeave: Leave;
  createLocation: Location;
  createPaymentConfig: PaymentConfig;
  createPaymentCorrection: PaymentCorrection;
  createShift?: Maybe<Scalars['Boolean']['output']>;
  createSignedUploadUrl: SignedUploadUrl;
  createTask: Task;
  createUserDocument: UserDocument;
  createUserProfile: UserProfile;
  indexFace: User;
  removeAllowance: Allowance;
  removeAnouncement: Anouncement;
  removeCheckpoint: Checkpoint;
  removeHoliday: Holiday;
  removeLocation: Location;
  removePaymentConfig: PaymentConfig;
  removePaymentCorrection: PaymentCorrection;
  removeRecurringShifts?: Maybe<Scalars['Boolean']['output']>;
  removeShift?: Maybe<Scalars['Boolean']['output']>;
  removeTask: Task;
  removeUserDocument: UserDocument;
  removeUserProfile: UserProfile;
  signIn: AuthOutput;
  signUp: AuthOutput;
  updateAllowance: Allowance;
  updateAnouncement: Anouncement;
  updateAttendance: Attendance;
  updateCheckpoint: Checkpoint;
  updateClaim: Claims;
  updateHoliday: Holiday;
  updateIncident: Incident;
  updateInventory: Inventory;
  updateInventoryRequest: InventoryRequest;
  updateLeave: Leave;
  updateLocation: Location;
  updatePaymentConfig: PaymentConfig;
  updatePaymentCorrection: PaymentCorrection;
  updateShift: Shift;
  updateTask: Task;
  updateUser: User;
  updateUserDocument: UserDocument;
  updateUserProfile: UserProfile;
};


export type MutationClearFaceArgs = {
  clearFaceInput: ClearFaceInput;
};


export type MutationClockInArgs = {
  clockInInput: ClockInInput;
};


export type MutationClockOutArgs = {
  clockOutInput: ClockOutInput;
};


export type MutationCreateAllowanceArgs = {
  createAllowanceInput: CreateAllowanceInput;
};


export type MutationCreateAnouncementArgs = {
  createAnouncementInput: CreateAnouncementInput;
};


export type MutationCreateAttendanceArgs = {
  createAttendanceInput: CreateAttendanceInput;
};


export type MutationCreateCheckpointArgs = {
  createCheckpointInput: CreateCheckpointInput;
};


export type MutationCreateCheckpointAttendanceArgs = {
  createCheckpointAttendanceInput: CreateCheckpointAttendanceInput;
};


export type MutationCreateClaimArgs = {
  input: ClaimInput;
};


export type MutationCreateFolderArgs = {
  createFolderInput: CreateFolderDto;
};


export type MutationCreateHolidayArgs = {
  createHolidayInput: CreateHolidayInput;
};


export type MutationCreateIncidentArgs = {
  createIncidentInput: CreateIncidentInput;
};


export type MutationCreateInventoryArgs = {
  createInventoryInput: CreateInventoryInput;
};


export type MutationCreateInventoryRequestArgs = {
  input: CreateInventoryRequestInput;
};


export type MutationCreateLeaveArgs = {
  createLeaveInput: CreateLeaveInput;
};


export type MutationCreateLocationArgs = {
  createLocationInput: CreateLocationInput;
};


export type MutationCreatePaymentConfigArgs = {
  createPaymentConfigInput: CreatePaymentConfigInput;
};


export type MutationCreatePaymentCorrectionArgs = {
  createPaymentCorrectionInput: CreatePaymentCorrectionInput;
};


export type MutationCreateShiftArgs = {
  createShiftInput: CreateShiftInput;
};


export type MutationCreateSignedUploadUrlArgs = {
  input: SignedUploadUrlInput;
};


export type MutationCreateTaskArgs = {
  createTaskInput: CreateTaskInput;
};


export type MutationCreateUserDocumentArgs = {
  createUserDocumentInput: CreateUserDocumentInput;
};


export type MutationCreateUserProfileArgs = {
  createUserProfileInput: CreateUserProfileInput;
};


export type MutationIndexFaceArgs = {
  indexFaceInput: IndexFaceInput;
};


export type MutationRemoveAllowanceArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveAnouncementArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCheckpointArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveHolidayArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveLocationArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemovePaymentConfigArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemovePaymentCorrectionArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveRecurringShiftsArgs = {
  id: Scalars['String']['input'];
  recurringId: Scalars['String']['input'];
};


export type MutationRemoveShiftArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveTaskArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveUserDocumentArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveUserProfileArgs = {
  id: Scalars['String']['input'];
};


export type MutationSignInArgs = {
  input: SignInInput;
};


export type MutationSignUpArgs = {
  input: CreateUserInput;
};


export type MutationUpdateAllowanceArgs = {
  updateAllowanceInput: UpdateAllowanceInput;
};


export type MutationUpdateAnouncementArgs = {
  id: Scalars['String']['input'];
  updateAnouncementInput: UpdateAnouncementInput;
};


export type MutationUpdateAttendanceArgs = {
  updateAttendanceInput: UpdateAttendanceInput;
};


export type MutationUpdateCheckpointArgs = {
  id: Scalars['String']['input'];
  updateCheckpointInput: UpdateCheckpointInput;
};


export type MutationUpdateClaimArgs = {
  id: Scalars['String']['input'];
  updateClaimInput: UpdateClaimInput;
};


export type MutationUpdateHolidayArgs = {
  id: Scalars['String']['input'];
  updateHolidayInput: UpdateHolidayInput;
};


export type MutationUpdateIncidentArgs = {
  id: Scalars['String']['input'];
  updateIncidentInput: UpdateIncidentInput;
};


export type MutationUpdateInventoryArgs = {
  id: Scalars['String']['input'];
  updateInventoryInput: CreateInventoryInput;
};


export type MutationUpdateInventoryRequestArgs = {
  id: Scalars['String']['input'];
  input: UpdateInventoryRequestInput;
};


export type MutationUpdateLeaveArgs = {
  id: Scalars['String']['input'];
  updateLeaveInput: UpdateLeaveInput;
};


export type MutationUpdateLocationArgs = {
  id: Scalars['ID']['input'];
  updateLocationInput: UpdateLocationInput;
};


export type MutationUpdatePaymentConfigArgs = {
  updatePaymentConfigInput: UpdatePaymentConfigInput;
};


export type MutationUpdatePaymentCorrectionArgs = {
  updatePaymentCorrectionInput: UpdatePaymentCorrectionInput;
};


export type MutationUpdateShiftArgs = {
  id: Scalars['String']['input'];
  updateShiftInput: UpdateShiftInput;
};


export type MutationUpdateTaskArgs = {
  updateTaskInput: UpdateTaskInput;
};


export type MutationUpdateUserArgs = {
  updateUserInput: UpdateUserInput;
};


export type MutationUpdateUserDocumentArgs = {
  id: Scalars['String']['input'];
  updateUserDocumentInput: UpdateUserDocumentInput;
};


export type MutationUpdateUserProfileArgs = {
  id: Scalars['String']['input'];
  updateUserProfileInput: UpdateUserProfileInput;
};

export type PaymentConfig = {
  __typename?: 'PaymentConfig';
  holiday: PaymentConfigOption;
  location: Location;
  roles: Array<UserRoles>;
  weekDay: PaymentConfigOption;
  weekOff: PaymentConfigOption;
};

export type PaymentConfigOption = {
  __typename?: 'PaymentConfigOption';
  fullTimeAmount: Scalars['Float']['output'];
  overTimeAmount: Scalars['Float']['output'];
  paymentType: PaymentType;
};

export type PaymentCorrection = {
  __typename?: 'PaymentCorrection';
  amount: Scalars['Float']['output'];
  correctedBy: User;
  date: Scalars['DateTime']['output'];
  reason: Scalars['String']['output'];
  user: User;
};

export enum PaymentType {
  Daily = 'DAILY',
  Hourly = 'HOURLY',
  Monthly = 'MONTHLY'
}

export type PresignedFields = {
  __typename?: 'PresignedFields';
  Policy: Scalars['String']['output'];
  acl: Scalars['String']['output'];
  algorithm: Scalars['String']['output'];
  bucket: Scalars['String']['output'];
  credential: Scalars['String']['output'];
  date: Scalars['String']['output'];
  key: Scalars['String']['output'];
  signature: Scalars['String']['output'];
};

export enum PriorityLevel {
  Critical = 'CRITICAL',
  High = 'HIGH',
  Low = 'LOW',
  Medium = 'MEDIUM'
}

export type Query = {
  __typename?: 'Query';
  allAttendances: Array<Attendance>;
  allowance: Allowance;
  allowances: Array<Allowance>;
  anouncement: Anouncement;
  anouncements: Array<Anouncement>;
  attendance: Array<Attendance>;
  checkpoint: Checkpoint;
  checkpointAttendance: CheckpointAttendance;
  checkpointAttendances: Array<CheckpointAttendance>;
  checkpoints: Array<Checkpoint>;
  claim: Claims;
  claims: Array<Claims>;
  getAttendanceById: Attendance;
  getCheckpointAttendances: Array<CheckpointAttendance>;
  getGuardAttendances: Array<CheckpointAttendance>;
  getLocationAttendances: Array<CheckpointAttendance>;
  getShiftsByLocation: Shift;
  getUserShifts: Shift;
  guardActivityStats: GuardActivityStats;
  guardAttendanceTrends: Array<TimeSeriesData>;
  health: Health;
  holiday: Holiday;
  holidays: Array<Holiday>;
  incident: Incident;
  incidents: Array<Incident>;
  inventory: Array<Inventory>;
  inventoryRequest: InventoryRequest;
  inventoryRequests: Array<InventoryRequest>;
  inventoryStats: Array<InventoryStats>;
  leave: Leave;
  leaveStats: LeaveStats;
  leaves: Array<Leave>;
  location: Location;
  locations: Array<Location>;
  /** Logged in  user */
  me: User;
  paymentConfig: PaymentConfig;
  paymentCorrection: PaymentCorrection;
  paymentCorrections: Array<PaymentCorrection>;
  shift: Shift;
  shifts: Array<Shift>;
  shiftsByUser: Array<Shift>;
  storageFolders: Array<Storage>;
  storageItems: Array<Storage>;
  systemFiles: Array<Storage>;
  task: Task;
  tasks: Array<Task>;
  user: User;
  userDocument: UserDocument;
  userDocuments: Array<UserDocument>;
  userProfile: UserProfile;
  userProfiles: Array<UserProfile>;
  users: Array<User>;
};


export type QueryAllowanceArgs = {
  id: Scalars['Int']['input'];
};


export type QueryAnouncementArgs = {
  id: Scalars['String']['input'];
};


export type QueryAttendanceArgs = {
  attendanceInput: AttendanceInput;
};


export type QueryCheckpointArgs = {
  id: Scalars['String']['input'];
};


export type QueryCheckpointAttendanceArgs = {
  id: Scalars['String']['input'];
};


export type QueryCheckpointsArgs = {
  filter?: InputMaybe<FindCheckpointsInput>;
};


export type QueryClaimArgs = {
  id: Scalars['String']['input'];
};


export type QueryClaimsArgs = {
  filter?: InputMaybe<FindClaimsInput>;
};


export type QueryGetAttendanceByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetCheckpointAttendancesArgs = {
  checkpointId: Scalars['String']['input'];
};


export type QueryGetGuardAttendancesArgs = {
  guardId: Scalars['String']['input'];
};


export type QueryGetLocationAttendancesArgs = {
  locationId: Scalars['String']['input'];
};


export type QueryGetShiftsByLocationArgs = {
  locationId: Scalars['String']['input'];
};


export type QueryGetUserShiftsArgs = {
  shiftsInput: ShiftsInput;
};


export type QueryGuardActivityStatsArgs = {
  filter?: InputMaybe<AnalyticsFilterInput>;
};


export type QueryGuardAttendanceTrendsArgs = {
  filter?: InputMaybe<AnalyticsFilterInput>;
};


export type QueryHolidayArgs = {
  id: Scalars['String']['input'];
};


export type QueryIncidentArgs = {
  id: Scalars['String']['input'];
};


export type QueryInventoryArgs = {
  inventoryInput?: InputMaybe<InventoryInput>;
};


export type QueryInventoryRequestArgs = {
  id: Scalars['String']['input'];
};


export type QueryInventoryRequestsArgs = {
  filter?: InputMaybe<FindInventoryRequestsInput>;
};


export type QueryLeaveArgs = {
  id: Scalars['String']['input'];
};


export type QueryLeaveStatsArgs = {
  filter?: InputMaybe<AnalyticsFilterInput>;
};


export type QueryLeavesArgs = {
  filter?: InputMaybe<FindLeavesInput>;
};


export type QueryLocationArgs = {
  id: Scalars['String']['input'];
};


export type QueryPaymentConfigArgs = {
  id: Scalars['Int']['input'];
};


export type QueryPaymentCorrectionArgs = {
  id: Scalars['Int']['input'];
};


export type QueryShiftArgs = {
  id: Scalars['String']['input'];
};


export type QueryShiftsArgs = {
  shiftsInput: ShiftsInput;
};


export type QueryShiftsByUserArgs = {
  userId: Scalars['String']['input'];
};


export type QueryStorageFoldersArgs = {
  parentId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryStorageItemsArgs = {
  parentId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTaskArgs = {
  id: Scalars['Int']['input'];
};


export type QueryUserArgs = {
  id: Scalars['String']['input'];
};


export type QueryUserDocumentArgs = {
  id: Scalars['String']['input'];
};


export type QueryUserDocumentsArgs = {
  filter?: InputMaybe<UserDocumentFilterInput>;
};


export type QueryUserProfileArgs = {
  id: Scalars['String']['input'];
};


export type QueryUsersArgs = {
  usersInput?: InputMaybe<UsersInput>;
};

export enum RequestStatus {
  Accepted = 'ACCEPTED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type RequestedItem = {
  __typename?: 'RequestedItem';
  costPrice: Scalars['Float']['output'];
  item: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  selectedAttributes: Array<SelectedAttribute>;
  sellingPrice: Scalars['Float']['output'];
  sku: Scalars['String']['output'];
};

export type RequestedItemInput = {
  item: Scalars['String']['input'];
  quantity: Scalars['Int']['input'];
  selectedAttributes: Array<SelectedAttributeInput>;
  sku: Scalars['String']['input'];
};

export type SelectedAttribute = {
  __typename?: 'SelectedAttribute';
  attributeName: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type SelectedAttributeInput = {
  attributeName: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type Shift = {
  __typename?: 'Shift';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  endDateTime: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  isRecurring?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Location>;
  overTime?: Maybe<Scalars['DateTime']['output']>;
  recurringId?: Maybe<Scalars['String']['output']>;
  startDateTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
  users?: Maybe<Array<User>>;
};

export type ShiftsInput = {
  endDateTime: Scalars['DateTime']['input'];
  locationId?: InputMaybe<Scalars['ID']['input']>;
  startDateTime: Scalars['DateTime']['input'];
};

export type SignInInput = {
  /** user password */
  password: Scalars['String']['input'];
  /** user phone number */
  phone: Scalars['String']['input'];
};

export type SignedUploadUrl = {
  __typename?: 'SignedUploadUrl';
  fields: PresignedFields;
  url: Scalars['String']['output'];
};

export type SignedUploadUrlInput = {
  contentType: Scalars['String']['input'];
  expiresIn?: InputMaybe<Scalars['Float']['input']>;
  key: Scalars['String']['input'];
};

export type SiteClaim = {
  __typename?: 'SiteClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  items: Array<Scalars['String']['output']>;
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
  site: Location;
  /** Claim created by */
  user: User;
};

export type Storage = {
  __typename?: 'Storage';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  metadata?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  parent?: Maybe<Storage>;
  path?: Maybe<Scalars['String']['output']>;
  size: Scalars['Float']['output'];
  source: StorageItemSource;
  type: StorageItemType;
  updatedAt: Scalars['DateTime']['output'];
};

export enum StorageItemSource {
  System = 'SYSTEM',
  User = 'USER'
}

export enum StorageItemType {
  File = 'FILE',
  Folder = 'FOLDER'
}

export type Task = {
  __typename?: 'Task';
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  isRecurring: Scalars['Boolean']['output'];
  location: Location;
  shift: Shift;
  shiftRecurringId?: Maybe<Scalars['String']['output']>;
  taskStatus: TaskStatus;
  title: Scalars['String']['output'];
  users: Array<User>;
};

export enum TaskStatus {
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  Pending = 'PENDING'
}

export type TimeSeriesData = {
  __typename?: 'TimeSeriesData';
  count: Scalars['Int']['output'];
  date: Scalars['String']['output'];
};

export type TravelClaim = {
  __typename?: 'TravelClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  client: Scalars['String']['output'];
  distance: Scalars['Int']['output'];
  from: Scalars['DateTime']['output'];
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
  to: Scalars['DateTime']['output'];
  toll: Scalars['String']['output'];
  /** Claim created by */
  user: User;
};

export type UpdateAllowanceInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdateAnouncementInput = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  document?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  userRoles?: InputMaybe<Array<UserRoles>>;
  users?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateAttendanceInput = {
  date: Scalars['DateTime']['input'];
  endTime: Scalars['DateTime']['input'];
  id: Scalars['String']['input'];
  locationId: Scalars['String']['input'];
  overTime: Scalars['DateTime']['input'];
  shiftId: Scalars['String']['input'];
  startTime: Scalars['DateTime']['input'];
  userId: Scalars['String']['input'];
};

export type UpdateCheckpointInput = {
  locationCoordinates?: InputMaybe<Array<Scalars['Float']['input']>>;
  name: Scalars['String']['input'];
};

export type UpdateClaimInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  claimType: ClaimType;
  client?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  distance?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['DateTime']['input']>;
  items?: InputMaybe<Array<Scalars['String']['input']>>;
  /** User ID of the person who processed the claim */
  processedBy?: InputMaybe<Scalars['String']['input']>;
  purpose?: InputMaybe<Scalars['String']['input']>;
  receipts?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Reason for rejection, if applicable */
  rejectedReason?: InputMaybe<Scalars['String']['input']>;
  site?: InputMaybe<Scalars['String']['input']>;
  /** Updated claim status */
  status?: InputMaybe<ClaimStatus>;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  toll?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
  workingHours?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateHolidayInput = {
  /** date of the holiday */
  date?: InputMaybe<Scalars['DateTime']['input']>;
  /** description of the holiday */
  description?: InputMaybe<Scalars['String']['input']>;
  /** name of the holiday */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateIncidentInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  evidence?: InputMaybe<Array<EvidenceInput>>;
  location?: InputMaybe<Scalars['String']['input']>;
  priorityLevel?: InputMaybe<PriorityLevel>;
};

export type UpdateInventoryRequestInput = {
  acceptedBy?: InputMaybe<Scalars['String']['input']>;
  status: RequestStatus;
};

export type UpdateLeaveInput = {
  approvedBy?: InputMaybe<Scalars['String']['input']>;
  leaveStatus: LeaveStatus;
  rejectedReason?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateLocationInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  emergencyContact?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePaymentConfigInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdatePaymentCorrectionInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdateShiftInput = {
  endDateTime?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['Int']['input'];
  isRecurring?: InputMaybe<Scalars['Boolean']['input']>;
  locationId?: InputMaybe<Scalars['ID']['input']>;
  overTime?: InputMaybe<Scalars['DateTime']['input']>;
  recurringId?: InputMaybe<Scalars['String']['input']>;
  startDateTime?: InputMaybe<Scalars['DateTime']['input']>;
  userIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type UpdateTaskInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdateUserDocumentInput = {
  documentName?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserInput = {
  faceInformation?: InputMaybe<FaceInformationInput>;
  /** user fullname */
  fullname?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  /** user password */
  password?: InputMaybe<Scalars['String']['input']>;
  /** user phone number */
  phone?: InputMaybe<Scalars['String']['input']>;
  /** user role */
  role?: InputMaybe<UserRoles>;
  /** user active status */
  userStatus?: InputMaybe<UserStatus>;
};

export type UpdateUserProfileInput = {
  ID?: InputMaybe<Scalars['String']['input']>;
  bankAccNumber?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  currentAddress?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  emergencyContact?: InputMaybe<Array<EmergencyContactInput>>;
  gender?: InputMaybe<Scalars['String']['input']>;
  ic?: InputMaybe<Scalars['String']['input']>;
  joinedAt?: InputMaybe<Scalars['DateTime']['input']>;
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  passport?: InputMaybe<Scalars['String']['input']>;
  passportExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitNumber?: InputMaybe<Scalars['String']['input']>;
  placeOfBirth?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type User = {
  __typename?: 'User';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  faceInformation?: Maybe<FaceInformation>;
  /** user fullname */
  fullname: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  password: Scalars['String']['output'];
  /** user phone number */
  phone: Scalars['String']['output'];
  /** user role */
  role: UserRoles;
  updatedAt: Scalars['DateTime']['output'];
  /** user active status */
  userStatus: UserStatus;
};

export type UserDocument = {
  __typename?: 'UserDocument';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  documentName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  updatedAt: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
  user: User;
};

export type UserDocumentFilterInput = {
  user?: InputMaybe<Scalars['String']['input']>;
};

export type UserProfile = {
  __typename?: 'UserProfile';
  ID?: Maybe<Scalars['String']['output']>;
  _id: Scalars['ID']['output'];
  bankAccNumber?: Maybe<Scalars['String']['output']>;
  bankName?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  currentAddress?: Maybe<Scalars['String']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  emergencyContact?: Maybe<Array<EmergencyContact>>;
  gender?: Maybe<Scalars['String']['output']>;
  ic?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  joinedAt?: Maybe<Scalars['DateTime']['output']>;
  maritalStatus?: Maybe<Scalars['String']['output']>;
  passport?: Maybe<Scalars['String']['output']>;
  passportExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  permitExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  permitNumber?: Maybe<Scalars['String']['output']>;
  placeOfBirth?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  user: Scalars['String']['output'];
};

export enum UserRoles {
  Admin = 'ADMIN',
  BufferGuard = 'BUFFER_GUARD',
  HrAdmin = 'HR_ADMIN',
  LocalGuard = 'LOCAL_GUARD',
  NepalGuard = 'NEPAL_GUARD',
  OperationsAdmin = 'OPERATIONS_ADMIN',
  OperationsManager = 'OPERATIONS_MANAGER'
}

export enum UserStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

export type UsersInput = {
  roles?: InputMaybe<Array<UserRoles>>;
};

export type GuardAttendanceTrendsQueryVariables = Exact<{
  filter: AnalyticsFilterInput;
}>;


export type GuardAttendanceTrendsQuery = { __typename?: 'Query', guardAttendanceTrends: Array<{ __typename?: 'TimeSeriesData', date: string, count: number }> };

export type GuardActivityStatsQueryVariables = Exact<{
  filter: AnalyticsFilterInput;
}>;


export type GuardActivityStatsQuery = { __typename?: 'Query', guardActivityStats: { __typename?: 'GuardActivityStats', totalAttendance: number, averageTimeSpent: number, totalCheckpoints: number, totalIncidents: number } };

export type LeaveStatsQueryVariables = Exact<{
  filter: AnalyticsFilterInput;
}>;


export type LeaveStatsQuery = { __typename?: 'Query', leaveStats: { __typename?: 'LeaveStats', pending: number, approved: number, rejected: number } };

export type InventoryStatsQueryVariables = Exact<{ [key: string]: never; }>;


export type InventoryStatsQuery = { __typename?: 'Query', inventoryStats: Array<{ __typename?: 'InventoryStats', type: string, totalItems: number, requestsPending: number }> };

export type AnnouncementsQueryVariables = Exact<{ [key: string]: never; }>;


export type AnnouncementsQuery = { __typename?: 'Query', anouncements: Array<{ __typename?: 'Anouncement', id: string, title: string, description: string, date: Date, userRoles?: Array<UserRoles> | null, document?: string | null, users?: Array<{ __typename?: 'User', id: string, fullname: string }> | null }> };

export type CreateAnnouncementMutationVariables = Exact<{
  createAnnouncementInput: CreateAnouncementInput;
}>;


export type CreateAnnouncementMutation = { __typename?: 'Mutation', createAnouncement: { __typename?: 'Anouncement', title: string, description: string, date: Date, userRoles?: Array<UserRoles> | null, document?: string | null } };

export type UpdateAnnoucementMutationVariables = Exact<{
  id: Scalars['String']['input'];
  updateAnouncementInput: UpdateAnouncementInput;
}>;


export type UpdateAnnoucementMutation = { __typename?: 'Mutation', updateAnouncement: { __typename?: 'Anouncement', id: string } };

export type AttendanceQueryVariables = Exact<{
  input: AttendanceInput;
}>;


export type AttendanceQuery = { __typename?: 'Query', attendance: Array<{ __typename?: 'Attendance', id: string, date: Date, timeSpentInMinutes: number, startTime: Date, endTime?: Date | null, overTime?: Date | null, shift?: { __typename?: 'Shift', id: string } | null, location?: { __typename?: 'Location', id: string, name: string } | null, user?: { __typename?: 'User', id: string, fullname: string } | null }> };

export type GetAllAttendanceQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllAttendanceQuery = { __typename?: 'Query', allAttendances: Array<{ __typename?: 'Attendance', id: string, date: Date, timeSpentInMinutes: number, overTimeSpentInMinutes: number, startTime: Date, endTime?: Date | null, overTime?: Date | null, shift?: { __typename?: 'Shift', id: string } | null, location?: { __typename?: 'Location', id: string, name: string } | null, user?: { __typename?: 'User', id: string, fullname: string } | null }> };

export type ClockInMutationVariables = Exact<{
  input: ClockInInput;
}>;


export type ClockInMutation = { __typename?: 'Mutation', clockIn: { __typename?: 'Attendance', id: string } };

export type ClockOutMutationVariables = Exact<{
  input: ClockOutInput;
}>;


export type ClockOutMutation = { __typename?: 'Mutation', clockOut: { __typename?: 'Attendance', id: string } };

export type CreateAttendanceMutationVariables = Exact<{
  input: CreateAttendanceInput;
}>;


export type CreateAttendanceMutation = { __typename?: 'Mutation', createAttendance: { __typename?: 'Attendance', id: string } };

export type GetAttendanceByIdQueryVariables = Exact<{
  attendanceId: Scalars['String']['input'];
}>;


export type GetAttendanceByIdQuery = { __typename?: 'Query', getAttendanceById: { __typename?: 'Attendance', id: string, date: Date, startTime: Date, endTime?: Date | null, overTime?: Date | null, location?: { __typename?: 'Location', id: string } | null, user?: { __typename?: 'User', id: string, fullname: string } | null, shift?: { __typename?: 'Shift', id: string, users?: Array<{ __typename?: 'User', id: string, fullname: string }> | null } | null } };

export type UpdateAttendanceMutationVariables = Exact<{
  input: UpdateAttendanceInput;
}>;


export type UpdateAttendanceMutation = { __typename?: 'Mutation', updateAttendance: { __typename?: 'Attendance', id: string } };

export type SigninMutationVariables = Exact<{
  input: SignInInput;
}>;


export type SigninMutation = { __typename?: 'Mutation', signIn: { __typename?: 'AuthOutput', access_token: string } };

export type CheckpointsQueryVariables = Exact<{
  filter: FindCheckpointsInput;
}>;


export type CheckpointsQuery = { __typename?: 'Query', checkpoints: Array<{ __typename?: 'Checkpoint', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, locationCoordinates?: Array<number> | null, location: { __typename?: 'Location', id: string, name: string } }> };

export type CreateCheckpointMutationVariables = Exact<{
  createCheckpointInput: CreateCheckpointInput;
}>;


export type CreateCheckpointMutation = { __typename?: 'Mutation', createCheckpoint: { __typename?: 'Checkpoint', _id: string, id: string, name: string } };

export type RemoveCheckpointMutationVariables = Exact<{
  checkpointId: Scalars['String']['input'];
}>;


export type RemoveCheckpointMutation = { __typename?: 'Mutation', removeCheckpoint: { __typename?: 'Checkpoint', id: string } };

export type CreateSignedUploadUrlMutationVariables = Exact<{
  input: SignedUploadUrlInput;
}>;


export type CreateSignedUploadUrlMutation = { __typename?: 'Mutation', createSignedUploadUrl: { __typename?: 'SignedUploadUrl', url: string, fields: { __typename?: 'PresignedFields', key: string, bucket: string, acl: string, algorithm: string, credential: string, date: string, Policy: string, signature: string } } };

export type HealthQueryVariables = Exact<{ [key: string]: never; }>;


export type HealthQuery = { __typename?: 'Query', health: { __typename?: 'Health', status: boolean } };

export type HolidaysQueryVariables = Exact<{ [key: string]: never; }>;


export type HolidaysQuery = { __typename?: 'Query', holidays: Array<{ __typename?: 'Holiday', id: string, name: string, date: Date, description: string }> };

export type CreateHolidayMutationVariables = Exact<{
  createHolidayInput: CreateHolidayInput;
}>;


export type CreateHolidayMutation = { __typename?: 'Mutation', createHoliday: { __typename?: 'Holiday', id: string } };

export type DeleteHolidayMutationVariables = Exact<{
  holidayId: Scalars['String']['input'];
}>;


export type DeleteHolidayMutation = { __typename?: 'Mutation', removeHoliday: { __typename?: 'Holiday', id: string } };

export type UpdateHolidayMutationVariables = Exact<{
  updateHolidayInput: UpdateHolidayInput;
  holidayId: Scalars['String']['input'];
}>;


export type UpdateHolidayMutation = { __typename?: 'Mutation', updateHoliday: { __typename?: 'Holiday', id: string } };

export type IncidentsQueryVariables = Exact<{ [key: string]: never; }>;


export type IncidentsQuery = { __typename?: 'Query', incidents: Array<{ __typename?: 'Incident', id: string, reportedAt: Date, description: string, priorityLevel: PriorityLevel, location: { __typename?: 'Location', name: string }, reportedBy: { __typename?: 'User', fullname: string }, evidence: Array<{ __typename?: 'Evidence', type: EvidenceType, url: string }> }> };

export type InventoryRequestQueryVariables = Exact<{
  input: FindInventoryRequestsInput;
}>;


export type InventoryRequestQuery = { __typename?: 'Query', inventoryRequests: Array<{ __typename?: 'InventoryRequest', _id: string, id: string, createdAt: Date, updatedAt: Date, acceptedAt?: Date | null, status: RequestStatus, inventoryType: InventoryType, requestedBy: { __typename?: 'User', fullname: string }, items: Array<{ __typename?: 'RequestedItem', item: string, sku: string, quantity: number, costPrice: number, sellingPrice: number, selectedAttributes: Array<{ __typename?: 'SelectedAttribute', attributeName: string, value: string }> }>, inventory: { __typename?: 'Inventory', item: string } }> };

export type UpdateInventoryRequestMutationVariables = Exact<{
  id: Scalars['String']['input'];
  UpdateInventoryRequestInput: UpdateInventoryRequestInput;
}>;


export type UpdateInventoryRequestMutation = { __typename?: 'Mutation', updateInventoryRequest: { __typename?: 'InventoryRequest', id: string } };

export type CreateInventoryMutationVariables = Exact<{
  input: CreateInventoryInput;
}>;


export type CreateInventoryMutation = { __typename?: 'Mutation', createInventory: { __typename?: 'Inventory', id: string } };

export type InventoryQueryVariables = Exact<{
  input: InventoryInput;
}>;


export type InventoryQuery = { __typename?: 'Query', inventory: Array<{ __typename?: 'Inventory', id: string, item: string, type: InventoryType }> };

export type LeavesQueryVariables = Exact<{ [key: string]: never; }>;


export type LeavesQuery = { __typename?: 'Query', leaves: Array<{ __typename?: 'Leave', _id: string, id: string, createdAt: Date, updatedAt: Date, reason: string, leaveType: LeaveType, startDateTime: Date, endDateTime: Date, leaveStatus?: LeaveStatus | null, rejectedReason?: string | null, user?: { __typename?: 'User', _id: string, id: string, fullname: string } | null }> };

export type CreateLeaveMutationVariables = Exact<{
  createLeaveInput: CreateLeaveInput;
}>;


export type CreateLeaveMutation = { __typename?: 'Mutation', createLeave: { __typename?: 'Leave', id: string } };

export type UpdateLeaveMutationVariables = Exact<{
  id: Scalars['String']['input'];
  updateLeaveInput: UpdateLeaveInput;
}>;


export type UpdateLeaveMutation = { __typename?: 'Mutation', updateLeave: { __typename?: 'Leave', id: string } };

export type GetLeaveByIdQueryVariables = Exact<{
  leaveId: Scalars['String']['input'];
}>;


export type GetLeaveByIdQuery = { __typename?: 'Query', leave: { __typename?: 'Leave', _id: string, id: string, leaveType: LeaveType, reason: string, startDateTime: Date, endDateTime: Date, leaveStatus?: LeaveStatus | null, rejectedReason?: string | null, user?: { __typename?: 'User', id: string, fullname: string } | null, approvedBy?: { __typename?: 'User', id: string, fullname: string } | null } };

export type LocationsQueryVariables = Exact<{ [key: string]: never; }>;


export type LocationsQuery = { __typename?: 'Query', locations: Array<{ __typename?: 'Location', id: string, name: string, description?: string | null, address?: string | null, emergencyContact?: string | null }> };

export type CreateLocationMutationVariables = Exact<{
  createLocationInput: CreateLocationInput;
}>;


export type CreateLocationMutation = { __typename?: 'Mutation', createLocation: { __typename?: 'Location', id: string } };

export type LocationQueryVariables = Exact<{
  locationId: Scalars['String']['input'];
}>;


export type LocationQuery = { __typename?: 'Query', location: { __typename?: 'Location', _id: string, id: string, createdAt: Date, updatedAt: Date, name: string, description?: string | null, address?: string | null, emergencyContact?: string | null } };

export type UpdateLocationMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  updateLocationInput: UpdateLocationInput;
}>;


export type UpdateLocationMutation = { __typename?: 'Mutation', updateLocation: { __typename?: 'Location', id: string } };

export type ShiftQueryVariables = Exact<{
  input: ShiftsInput;
}>;


export type ShiftQuery = { __typename?: 'Query', shifts: Array<{ __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location?: { __typename?: 'Location', name: string, id: string } | null, users?: Array<{ __typename?: 'User', fullname: string, id: string }> | null }> };

export type CreateShiftMutationVariables = Exact<{
  input: CreateShiftInput;
}>;


export type CreateShiftMutation = { __typename?: 'Mutation', createShift?: boolean | null };

export type UpdateShiftMutationVariables = Exact<{
  id: Scalars['String']['input'];
  input: UpdateShiftInput;
}>;


export type UpdateShiftMutation = { __typename?: 'Mutation', updateShift: { __typename?: 'Shift', id: string } };

export type DeleteShiftMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type DeleteShiftMutation = { __typename?: 'Mutation', removeShift?: boolean | null };

export type DeleteRecurringShiftMutationVariables = Exact<{
  id: Scalars['String']['input'];
  recurringId: Scalars['String']['input'];
}>;


export type DeleteRecurringShiftMutation = { __typename?: 'Mutation', removeRecurringShifts?: boolean | null };

export type StorageFoldersQueryVariables = Exact<{
  parentId?: InputMaybe<Scalars['String']['input']>;
}>;


export type StorageFoldersQuery = { __typename?: 'Query', storageFolders: Array<{ __typename?: 'Storage', id: string, name: string, type: StorageItemType, size: number, path?: string | null, metadata?: string | null }> };

export type StorageItemsQueryVariables = Exact<{
  parentId?: InputMaybe<Scalars['String']['input']>;
}>;


export type StorageItemsQuery = { __typename?: 'Query', storageItems: Array<{ __typename?: 'Storage', id: string, name: string, type: StorageItemType, size: number, path?: string | null, metadata?: string | null }> };

export type CreateFolderMutationVariables = Exact<{
  createFolderInput: CreateFolderDto;
}>;


export type CreateFolderMutation = { __typename?: 'Mutation', createFolder: { __typename?: 'Storage', _id: string } };

export type StorageSystemFoldersQueryVariables = Exact<{ [key: string]: never; }>;


export type StorageSystemFoldersQuery = { __typename?: 'Query', systemFiles: Array<{ __typename?: 'Storage', id: string, name: string, path?: string | null, parent?: { __typename?: 'Storage', id: string } | null }> };

export type CreateUserDocumentMutationVariables = Exact<{
  input: CreateUserDocumentInput;
}>;


export type CreateUserDocumentMutation = { __typename?: 'Mutation', createUserDocument: { __typename?: 'UserDocument', id: string } };

export type DeleteUserDocumentMutationVariables = Exact<{
  documentId: Scalars['String']['input'];
}>;


export type DeleteUserDocumentMutation = { __typename?: 'Mutation', removeUserDocument: { __typename?: 'UserDocument', id: string, documentName: string } };

export type UserDocumentsQueryVariables = Exact<{
  userDocumentFilterInput: UserDocumentFilterInput;
}>;


export type UserDocumentsQuery = { __typename?: 'Query', userDocuments: Array<{ __typename?: 'UserDocument', id: string, documentName: string, url: string }> };

export type UpdateUserProfileMutationVariables = Exact<{
  userId: Scalars['String']['input'];
  updateUserProfileInput: UpdateUserProfileInput;
}>;


export type UpdateUserProfileMutation = { __typename?: 'Mutation', updateUserProfile: { __typename?: 'UserProfile', id: string } };

export type UserProfileQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type UserProfileQuery = { __typename?: 'Query', userProfile: { __typename?: 'UserProfile', _id: string, permitExpiresAt?: Date | null, gender?: string | null, dob?: Date | null, placeOfBirth?: string | null, currentAddress?: string | null, joinedAt?: Date | null, maritalStatus?: string | null, bankAccNumber?: string | null, bankName?: string | null, id: string, ID?: string | null, ic?: string | null, passport?: string | null, passportExpiresAt?: Date | null, permitNumber?: string | null, emergencyContact?: Array<{ __typename?: 'EmergencyContact', name: string, relation: string, contact: { __typename?: 'Contact', countryCode: string, phone: string } }> | null } };

export type UsersQueryVariables = Exact<{
  input: UsersInput;
}>;


export type UsersQuery = { __typename?: 'Query', users: Array<{ __typename?: 'User', id: string, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles }> };

export type CreateUserMutationVariables = Exact<{
  input: CreateUserInput;
}>;


export type CreateUserMutation = { __typename?: 'Mutation', signUp: { __typename?: 'AuthOutput', access_token: string } };

export type UserQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type UserQuery = { __typename?: 'Query', user: { __typename?: 'User', id: string, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles } };

export type UpdateUserMutationVariables = Exact<{
  input: UpdateUserInput;
}>;


export type UpdateUserMutation = { __typename?: 'Mutation', updateUser: { __typename?: 'User', id: string } };

export type MeQueryVariables = Exact<{ [key: string]: never; }>;


export type MeQuery = { __typename?: 'Query', me: { __typename?: 'User', _id: string, id: string, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles } };

export type IndexFaceMutationVariables = Exact<{
  indexFaceInput: IndexFaceInput;
}>;


export type IndexFaceMutation = { __typename?: 'Mutation', indexFace: { __typename?: 'User', id: string, fullname: string, userStatus: UserStatus, role: UserRoles } };

export type ClearFaceMutationVariables = Exact<{
  input: ClearFaceInput;
}>;


export type ClearFaceMutation = { __typename?: 'Mutation', clearFace: boolean };



export const GuardAttendanceTrendsDocument = `
    query GuardAttendanceTrends($filter: AnalyticsFilterInput!) {
  guardAttendanceTrends(filter: $filter) {
    date
    count
  }
}
    `;

export const useGuardAttendanceTrendsQuery = <
      TData = GuardAttendanceTrendsQuery,
      TError = Error
    >(
      variables: GuardAttendanceTrendsQueryVariables,
      options?: Omit<UseQueryOptions<GuardAttendanceTrendsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GuardAttendanceTrendsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GuardAttendanceTrendsQuery, TError, TData>(
      {
    queryKey: ['GuardAttendanceTrends', variables],
    queryFn: fetchData<GuardAttendanceTrendsQuery, GuardAttendanceTrendsQueryVariables>(GuardAttendanceTrendsDocument, variables),
    ...options
  }
    )};

useGuardAttendanceTrendsQuery.document = GuardAttendanceTrendsDocument;

useGuardAttendanceTrendsQuery.getKey = (variables: GuardAttendanceTrendsQueryVariables) => ['GuardAttendanceTrends', variables];

export const GuardActivityStatsDocument = `
    query GuardActivityStats($filter: AnalyticsFilterInput!) {
  guardActivityStats(filter: $filter) {
    totalAttendance
    averageTimeSpent
    totalCheckpoints
    totalIncidents
  }
}
    `;

export const useGuardActivityStatsQuery = <
      TData = GuardActivityStatsQuery,
      TError = Error
    >(
      variables: GuardActivityStatsQueryVariables,
      options?: Omit<UseQueryOptions<GuardActivityStatsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GuardActivityStatsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GuardActivityStatsQuery, TError, TData>(
      {
    queryKey: ['GuardActivityStats', variables],
    queryFn: fetchData<GuardActivityStatsQuery, GuardActivityStatsQueryVariables>(GuardActivityStatsDocument, variables),
    ...options
  }
    )};

useGuardActivityStatsQuery.document = GuardActivityStatsDocument;

useGuardActivityStatsQuery.getKey = (variables: GuardActivityStatsQueryVariables) => ['GuardActivityStats', variables];

export const LeaveStatsDocument = `
    query LeaveStats($filter: AnalyticsFilterInput!) {
  leaveStats(filter: $filter) {
    pending
    approved
    rejected
  }
}
    `;

export const useLeaveStatsQuery = <
      TData = LeaveStatsQuery,
      TError = Error
    >(
      variables: LeaveStatsQueryVariables,
      options?: Omit<UseQueryOptions<LeaveStatsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<LeaveStatsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<LeaveStatsQuery, TError, TData>(
      {
    queryKey: ['LeaveStats', variables],
    queryFn: fetchData<LeaveStatsQuery, LeaveStatsQueryVariables>(LeaveStatsDocument, variables),
    ...options
  }
    )};

useLeaveStatsQuery.document = LeaveStatsDocument;

useLeaveStatsQuery.getKey = (variables: LeaveStatsQueryVariables) => ['LeaveStats', variables];

export const InventoryStatsDocument = `
    query InventoryStats {
  inventoryStats {
    type
    totalItems
    requestsPending
  }
}
    `;

export const useInventoryStatsQuery = <
      TData = InventoryStatsQuery,
      TError = Error
    >(
      variables?: InventoryStatsQueryVariables,
      options?: Omit<UseQueryOptions<InventoryStatsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<InventoryStatsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<InventoryStatsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['InventoryStats'] : ['InventoryStats', variables],
    queryFn: fetchData<InventoryStatsQuery, InventoryStatsQueryVariables>(InventoryStatsDocument, variables),
    ...options
  }
    )};

useInventoryStatsQuery.document = InventoryStatsDocument;

useInventoryStatsQuery.getKey = (variables?: InventoryStatsQueryVariables) => variables === undefined ? ['InventoryStats'] : ['InventoryStats', variables];

export const AnnouncementsDocument = `
    query Announcements {
  anouncements {
    id
    title
    description
    date
    userRoles
    document
    users {
      id
      fullname
    }
  }
}
    `;

export const useAnnouncementsQuery = <
      TData = AnnouncementsQuery,
      TError = Error
    >(
      variables?: AnnouncementsQueryVariables,
      options?: Omit<UseQueryOptions<AnnouncementsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<AnnouncementsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<AnnouncementsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Announcements'] : ['Announcements', variables],
    queryFn: fetchData<AnnouncementsQuery, AnnouncementsQueryVariables>(AnnouncementsDocument, variables),
    ...options
  }
    )};

useAnnouncementsQuery.document = AnnouncementsDocument;

useAnnouncementsQuery.getKey = (variables?: AnnouncementsQueryVariables) => variables === undefined ? ['Announcements'] : ['Announcements', variables];

export const CreateAnnouncementDocument = `
    mutation CreateAnnouncement($createAnnouncementInput: CreateAnouncementInput!) {
  createAnouncement(createAnouncementInput: $createAnnouncementInput) {
    title
    description
    date
    userRoles
    document
  }
}
    `;

export const useCreateAnnouncementMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateAnnouncementMutation, TError, CreateAnnouncementMutationVariables, TContext>) => {
    
    return useMutation<CreateAnnouncementMutation, TError, CreateAnnouncementMutationVariables, TContext>(
      {
    mutationKey: ['CreateAnnouncement'],
    mutationFn: (variables?: CreateAnnouncementMutationVariables) => fetchData<CreateAnnouncementMutation, CreateAnnouncementMutationVariables>(CreateAnnouncementDocument, variables)(),
    ...options
  }
    )};

export const UpdateAnnoucementDocument = `
    mutation UpdateAnnoucement($id: String!, $updateAnouncementInput: UpdateAnouncementInput!) {
  updateAnouncement(id: $id, updateAnouncementInput: $updateAnouncementInput) {
    id
  }
}
    `;

export const useUpdateAnnoucementMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateAnnoucementMutation, TError, UpdateAnnoucementMutationVariables, TContext>) => {
    
    return useMutation<UpdateAnnoucementMutation, TError, UpdateAnnoucementMutationVariables, TContext>(
      {
    mutationKey: ['UpdateAnnoucement'],
    mutationFn: (variables?: UpdateAnnoucementMutationVariables) => fetchData<UpdateAnnoucementMutation, UpdateAnnoucementMutationVariables>(UpdateAnnoucementDocument, variables)(),
    ...options
  }
    )};

export const AttendanceDocument = `
    query Attendance($input: AttendanceInput!) {
  attendance(attendanceInput: $input) {
    id
    date
    timeSpentInMinutes
    startTime
    endTime
    overTime
    shift {
      id
    }
    location {
      id
      name
    }
    user {
      id
      fullname
    }
  }
}
    `;

export const useAttendanceQuery = <
      TData = AttendanceQuery,
      TError = Error
    >(
      variables: AttendanceQueryVariables,
      options?: Omit<UseQueryOptions<AttendanceQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<AttendanceQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<AttendanceQuery, TError, TData>(
      {
    queryKey: ['Attendance', variables],
    queryFn: fetchData<AttendanceQuery, AttendanceQueryVariables>(AttendanceDocument, variables),
    ...options
  }
    )};

useAttendanceQuery.document = AttendanceDocument;

useAttendanceQuery.getKey = (variables: AttendanceQueryVariables) => ['Attendance', variables];

export const GetAllAttendanceDocument = `
    query GetAllAttendance {
  allAttendances {
    id
    date
    timeSpentInMinutes
    overTimeSpentInMinutes
    startTime
    endTime
    overTime
    shift {
      id
    }
    location {
      id
      name
    }
    user {
      id
      fullname
    }
  }
}
    `;

export const useGetAllAttendanceQuery = <
      TData = GetAllAttendanceQuery,
      TError = Error
    >(
      variables?: GetAllAttendanceQueryVariables,
      options?: Omit<UseQueryOptions<GetAllAttendanceQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GetAllAttendanceQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GetAllAttendanceQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['GetAllAttendance'] : ['GetAllAttendance', variables],
    queryFn: fetchData<GetAllAttendanceQuery, GetAllAttendanceQueryVariables>(GetAllAttendanceDocument, variables),
    ...options
  }
    )};

useGetAllAttendanceQuery.document = GetAllAttendanceDocument;

useGetAllAttendanceQuery.getKey = (variables?: GetAllAttendanceQueryVariables) => variables === undefined ? ['GetAllAttendance'] : ['GetAllAttendance', variables];

export const ClockInDocument = `
    mutation ClockIn($input: ClockInInput!) {
  clockIn(clockInInput: $input) {
    id
  }
}
    `;

export const useClockInMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClockInMutation, TError, ClockInMutationVariables, TContext>) => {
    
    return useMutation<ClockInMutation, TError, ClockInMutationVariables, TContext>(
      {
    mutationKey: ['ClockIn'],
    mutationFn: (variables?: ClockInMutationVariables) => fetchData<ClockInMutation, ClockInMutationVariables>(ClockInDocument, variables)(),
    ...options
  }
    )};

export const ClockOutDocument = `
    mutation ClockOut($input: ClockOutInput!) {
  clockOut(clockOutInput: $input) {
    id
  }
}
    `;

export const useClockOutMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClockOutMutation, TError, ClockOutMutationVariables, TContext>) => {
    
    return useMutation<ClockOutMutation, TError, ClockOutMutationVariables, TContext>(
      {
    mutationKey: ['ClockOut'],
    mutationFn: (variables?: ClockOutMutationVariables) => fetchData<ClockOutMutation, ClockOutMutationVariables>(ClockOutDocument, variables)(),
    ...options
  }
    )};

export const CreateAttendanceDocument = `
    mutation CreateAttendance($input: CreateAttendanceInput!) {
  createAttendance(createAttendanceInput: $input) {
    id
  }
}
    `;

export const useCreateAttendanceMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateAttendanceMutation, TError, CreateAttendanceMutationVariables, TContext>) => {
    
    return useMutation<CreateAttendanceMutation, TError, CreateAttendanceMutationVariables, TContext>(
      {
    mutationKey: ['CreateAttendance'],
    mutationFn: (variables?: CreateAttendanceMutationVariables) => fetchData<CreateAttendanceMutation, CreateAttendanceMutationVariables>(CreateAttendanceDocument, variables)(),
    ...options
  }
    )};

export const GetAttendanceByIdDocument = `
    query GetAttendanceById($attendanceId: String!) {
  getAttendanceById(id: $attendanceId) {
    id
    date
    startTime
    endTime
    overTime
    location {
      id
    }
    user {
      id
      fullname
    }
    shift {
      id
      users {
        id
        fullname
      }
    }
  }
}
    `;

export const useGetAttendanceByIdQuery = <
      TData = GetAttendanceByIdQuery,
      TError = Error
    >(
      variables: GetAttendanceByIdQueryVariables,
      options?: Omit<UseQueryOptions<GetAttendanceByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GetAttendanceByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GetAttendanceByIdQuery, TError, TData>(
      {
    queryKey: ['GetAttendanceById', variables],
    queryFn: fetchData<GetAttendanceByIdQuery, GetAttendanceByIdQueryVariables>(GetAttendanceByIdDocument, variables),
    ...options
  }
    )};

useGetAttendanceByIdQuery.document = GetAttendanceByIdDocument;

useGetAttendanceByIdQuery.getKey = (variables: GetAttendanceByIdQueryVariables) => ['GetAttendanceById', variables];

export const UpdateAttendanceDocument = `
    mutation UpdateAttendance($input: UpdateAttendanceInput!) {
  updateAttendance(updateAttendanceInput: $input) {
    id
  }
}
    `;

export const useUpdateAttendanceMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateAttendanceMutation, TError, UpdateAttendanceMutationVariables, TContext>) => {
    
    return useMutation<UpdateAttendanceMutation, TError, UpdateAttendanceMutationVariables, TContext>(
      {
    mutationKey: ['UpdateAttendance'],
    mutationFn: (variables?: UpdateAttendanceMutationVariables) => fetchData<UpdateAttendanceMutation, UpdateAttendanceMutationVariables>(UpdateAttendanceDocument, variables)(),
    ...options
  }
    )};

export const SigninDocument = `
    mutation Signin($input: SignInInput!) {
  signIn(input: $input) {
    access_token
  }
}
    `;

export const useSigninMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<SigninMutation, TError, SigninMutationVariables, TContext>) => {
    
    return useMutation<SigninMutation, TError, SigninMutationVariables, TContext>(
      {
    mutationKey: ['Signin'],
    mutationFn: (variables?: SigninMutationVariables) => fetchData<SigninMutation, SigninMutationVariables>(SigninDocument, variables)(),
    ...options
  }
    )};

export const CheckpointsDocument = `
    query Checkpoints($filter: FindCheckpointsInput!) {
  checkpoints(filter: $filter) {
    _id
    id
    createdAt
    updatedAt
    name
    location {
      id
      name
    }
    locationCoordinates
  }
}
    `;

export const useCheckpointsQuery = <
      TData = CheckpointsQuery,
      TError = Error
    >(
      variables: CheckpointsQueryVariables,
      options?: Omit<UseQueryOptions<CheckpointsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<CheckpointsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<CheckpointsQuery, TError, TData>(
      {
    queryKey: ['Checkpoints', variables],
    queryFn: fetchData<CheckpointsQuery, CheckpointsQueryVariables>(CheckpointsDocument, variables),
    ...options
  }
    )};

useCheckpointsQuery.document = CheckpointsDocument;

useCheckpointsQuery.getKey = (variables: CheckpointsQueryVariables) => ['Checkpoints', variables];

export const CreateCheckpointDocument = `
    mutation CreateCheckpoint($createCheckpointInput: CreateCheckpointInput!) {
  createCheckpoint(createCheckpointInput: $createCheckpointInput) {
    _id
    id
    name
  }
}
    `;

export const useCreateCheckpointMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateCheckpointMutation, TError, CreateCheckpointMutationVariables, TContext>) => {
    
    return useMutation<CreateCheckpointMutation, TError, CreateCheckpointMutationVariables, TContext>(
      {
    mutationKey: ['CreateCheckpoint'],
    mutationFn: (variables?: CreateCheckpointMutationVariables) => fetchData<CreateCheckpointMutation, CreateCheckpointMutationVariables>(CreateCheckpointDocument, variables)(),
    ...options
  }
    )};

export const RemoveCheckpointDocument = `
    mutation RemoveCheckpoint($checkpointId: String!) {
  removeCheckpoint(id: $checkpointId) {
    id
  }
}
    `;

export const useRemoveCheckpointMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<RemoveCheckpointMutation, TError, RemoveCheckpointMutationVariables, TContext>) => {
    
    return useMutation<RemoveCheckpointMutation, TError, RemoveCheckpointMutationVariables, TContext>(
      {
    mutationKey: ['RemoveCheckpoint'],
    mutationFn: (variables?: RemoveCheckpointMutationVariables) => fetchData<RemoveCheckpointMutation, RemoveCheckpointMutationVariables>(RemoveCheckpointDocument, variables)(),
    ...options
  }
    )};

export const CreateSignedUploadUrlDocument = `
    mutation CreateSignedUploadUrl($input: SignedUploadUrlInput!) {
  createSignedUploadUrl(input: $input) {
    url
    fields {
      key
      bucket
      acl
      algorithm
      credential
      date
      Policy
      signature
    }
  }
}
    `;

export const useCreateSignedUploadUrlMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateSignedUploadUrlMutation, TError, CreateSignedUploadUrlMutationVariables, TContext>) => {
    
    return useMutation<CreateSignedUploadUrlMutation, TError, CreateSignedUploadUrlMutationVariables, TContext>(
      {
    mutationKey: ['CreateSignedUploadUrl'],
    mutationFn: (variables?: CreateSignedUploadUrlMutationVariables) => fetchData<CreateSignedUploadUrlMutation, CreateSignedUploadUrlMutationVariables>(CreateSignedUploadUrlDocument, variables)(),
    ...options
  }
    )};

export const HealthDocument = `
    query Health {
  health {
    status
  }
}
    `;

export const useHealthQuery = <
      TData = HealthQuery,
      TError = Error
    >(
      variables?: HealthQueryVariables,
      options?: Omit<UseQueryOptions<HealthQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<HealthQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<HealthQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Health'] : ['Health', variables],
    queryFn: fetchData<HealthQuery, HealthQueryVariables>(HealthDocument, variables),
    ...options
  }
    )};

useHealthQuery.document = HealthDocument;

useHealthQuery.getKey = (variables?: HealthQueryVariables) => variables === undefined ? ['Health'] : ['Health', variables];

export const HolidaysDocument = `
    query Holidays {
  holidays {
    id
    name
    date
    description
  }
}
    `;

export const useHolidaysQuery = <
      TData = HolidaysQuery,
      TError = Error
    >(
      variables?: HolidaysQueryVariables,
      options?: Omit<UseQueryOptions<HolidaysQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<HolidaysQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<HolidaysQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Holidays'] : ['Holidays', variables],
    queryFn: fetchData<HolidaysQuery, HolidaysQueryVariables>(HolidaysDocument, variables),
    ...options
  }
    )};

useHolidaysQuery.document = HolidaysDocument;

useHolidaysQuery.getKey = (variables?: HolidaysQueryVariables) => variables === undefined ? ['Holidays'] : ['Holidays', variables];

export const CreateHolidayDocument = `
    mutation CreateHoliday($createHolidayInput: CreateHolidayInput!) {
  createHoliday(createHolidayInput: $createHolidayInput) {
    id
  }
}
    `;

export const useCreateHolidayMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateHolidayMutation, TError, CreateHolidayMutationVariables, TContext>) => {
    
    return useMutation<CreateHolidayMutation, TError, CreateHolidayMutationVariables, TContext>(
      {
    mutationKey: ['CreateHoliday'],
    mutationFn: (variables?: CreateHolidayMutationVariables) => fetchData<CreateHolidayMutation, CreateHolidayMutationVariables>(CreateHolidayDocument, variables)(),
    ...options
  }
    )};

export const DeleteHolidayDocument = `
    mutation DeleteHoliday($holidayId: String!) {
  removeHoliday(id: $holidayId) {
    id
  }
}
    `;

export const useDeleteHolidayMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<DeleteHolidayMutation, TError, DeleteHolidayMutationVariables, TContext>) => {
    
    return useMutation<DeleteHolidayMutation, TError, DeleteHolidayMutationVariables, TContext>(
      {
    mutationKey: ['DeleteHoliday'],
    mutationFn: (variables?: DeleteHolidayMutationVariables) => fetchData<DeleteHolidayMutation, DeleteHolidayMutationVariables>(DeleteHolidayDocument, variables)(),
    ...options
  }
    )};

export const UpdateHolidayDocument = `
    mutation UpdateHoliday($updateHolidayInput: UpdateHolidayInput!, $holidayId: String!) {
  updateHoliday(id: $holidayId, updateHolidayInput: $updateHolidayInput) {
    id
  }
}
    `;

export const useUpdateHolidayMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateHolidayMutation, TError, UpdateHolidayMutationVariables, TContext>) => {
    
    return useMutation<UpdateHolidayMutation, TError, UpdateHolidayMutationVariables, TContext>(
      {
    mutationKey: ['UpdateHoliday'],
    mutationFn: (variables?: UpdateHolidayMutationVariables) => fetchData<UpdateHolidayMutation, UpdateHolidayMutationVariables>(UpdateHolidayDocument, variables)(),
    ...options
  }
    )};

export const IncidentsDocument = `
    query Incidents {
  incidents {
    id
    location {
      name
    }
    reportedAt
    description
    priorityLevel
    reportedBy {
      fullname
    }
    evidence {
      type
      url
    }
  }
}
    `;

export const useIncidentsQuery = <
      TData = IncidentsQuery,
      TError = Error
    >(
      variables?: IncidentsQueryVariables,
      options?: Omit<UseQueryOptions<IncidentsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<IncidentsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<IncidentsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Incidents'] : ['Incidents', variables],
    queryFn: fetchData<IncidentsQuery, IncidentsQueryVariables>(IncidentsDocument, variables),
    ...options
  }
    )};

useIncidentsQuery.document = IncidentsDocument;

useIncidentsQuery.getKey = (variables?: IncidentsQueryVariables) => variables === undefined ? ['Incidents'] : ['Incidents', variables];

export const InventoryRequestDocument = `
    query InventoryRequest($input: FindInventoryRequestsInput!) {
  inventoryRequests(filter: $input) {
    _id
    id
    createdAt
    updatedAt
    requestedBy {
      fullname
    }
    items {
      item
      sku
      quantity
      costPrice
      sellingPrice
      selectedAttributes {
        attributeName
        value
      }
    }
    acceptedAt
    status
    inventoryType
    inventory {
      item
    }
  }
}
    `;

export const useInventoryRequestQuery = <
      TData = InventoryRequestQuery,
      TError = Error
    >(
      variables: InventoryRequestQueryVariables,
      options?: Omit<UseQueryOptions<InventoryRequestQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<InventoryRequestQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<InventoryRequestQuery, TError, TData>(
      {
    queryKey: ['InventoryRequest', variables],
    queryFn: fetchData<InventoryRequestQuery, InventoryRequestQueryVariables>(InventoryRequestDocument, variables),
    ...options
  }
    )};

useInventoryRequestQuery.document = InventoryRequestDocument;

useInventoryRequestQuery.getKey = (variables: InventoryRequestQueryVariables) => ['InventoryRequest', variables];

export const UpdateInventoryRequestDocument = `
    mutation UpdateInventoryRequest($id: String!, $UpdateInventoryRequestInput: UpdateInventoryRequestInput!) {
  updateInventoryRequest(id: $id, input: $UpdateInventoryRequestInput) {
    id
  }
}
    `;

export const useUpdateInventoryRequestMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateInventoryRequestMutation, TError, UpdateInventoryRequestMutationVariables, TContext>) => {
    
    return useMutation<UpdateInventoryRequestMutation, TError, UpdateInventoryRequestMutationVariables, TContext>(
      {
    mutationKey: ['UpdateInventoryRequest'],
    mutationFn: (variables?: UpdateInventoryRequestMutationVariables) => fetchData<UpdateInventoryRequestMutation, UpdateInventoryRequestMutationVariables>(UpdateInventoryRequestDocument, variables)(),
    ...options
  }
    )};

export const CreateInventoryDocument = `
    mutation CreateInventory($input: CreateInventoryInput!) {
  createInventory(createInventoryInput: $input) {
    id
  }
}
    `;

export const useCreateInventoryMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateInventoryMutation, TError, CreateInventoryMutationVariables, TContext>) => {
    
    return useMutation<CreateInventoryMutation, TError, CreateInventoryMutationVariables, TContext>(
      {
    mutationKey: ['CreateInventory'],
    mutationFn: (variables?: CreateInventoryMutationVariables) => fetchData<CreateInventoryMutation, CreateInventoryMutationVariables>(CreateInventoryDocument, variables)(),
    ...options
  }
    )};

export const InventoryDocument = `
    query Inventory($input: InventoryInput!) {
  inventory(inventoryInput: $input) {
    id
    item
    type
  }
}
    `;

export const useInventoryQuery = <
      TData = InventoryQuery,
      TError = Error
    >(
      variables: InventoryQueryVariables,
      options?: Omit<UseQueryOptions<InventoryQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<InventoryQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<InventoryQuery, TError, TData>(
      {
    queryKey: ['Inventory', variables],
    queryFn: fetchData<InventoryQuery, InventoryQueryVariables>(InventoryDocument, variables),
    ...options
  }
    )};

useInventoryQuery.document = InventoryDocument;

useInventoryQuery.getKey = (variables: InventoryQueryVariables) => ['Inventory', variables];

export const LeavesDocument = `
    query Leaves {
  leaves {
    _id
    id
    createdAt
    updatedAt
    reason
    leaveType
    startDateTime
    endDateTime
    leaveStatus
    rejectedReason
    user {
      _id
      id
      fullname
    }
  }
}
    `;

export const useLeavesQuery = <
      TData = LeavesQuery,
      TError = Error
    >(
      variables?: LeavesQueryVariables,
      options?: Omit<UseQueryOptions<LeavesQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<LeavesQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<LeavesQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Leaves'] : ['Leaves', variables],
    queryFn: fetchData<LeavesQuery, LeavesQueryVariables>(LeavesDocument, variables),
    ...options
  }
    )};

useLeavesQuery.document = LeavesDocument;

useLeavesQuery.getKey = (variables?: LeavesQueryVariables) => variables === undefined ? ['Leaves'] : ['Leaves', variables];

export const CreateLeaveDocument = `
    mutation CreateLeave($createLeaveInput: CreateLeaveInput!) {
  createLeave(createLeaveInput: $createLeaveInput) {
    id
  }
}
    `;

export const useCreateLeaveMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateLeaveMutation, TError, CreateLeaveMutationVariables, TContext>) => {
    
    return useMutation<CreateLeaveMutation, TError, CreateLeaveMutationVariables, TContext>(
      {
    mutationKey: ['CreateLeave'],
    mutationFn: (variables?: CreateLeaveMutationVariables) => fetchData<CreateLeaveMutation, CreateLeaveMutationVariables>(CreateLeaveDocument, variables)(),
    ...options
  }
    )};

export const UpdateLeaveDocument = `
    mutation UpdateLeave($id: String!, $updateLeaveInput: UpdateLeaveInput!) {
  updateLeave(id: $id, updateLeaveInput: $updateLeaveInput) {
    id
  }
}
    `;

export const useUpdateLeaveMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateLeaveMutation, TError, UpdateLeaveMutationVariables, TContext>) => {
    
    return useMutation<UpdateLeaveMutation, TError, UpdateLeaveMutationVariables, TContext>(
      {
    mutationKey: ['UpdateLeave'],
    mutationFn: (variables?: UpdateLeaveMutationVariables) => fetchData<UpdateLeaveMutation, UpdateLeaveMutationVariables>(UpdateLeaveDocument, variables)(),
    ...options
  }
    )};

export const GetLeaveByIdDocument = `
    query GetLeaveById($leaveId: String!) {
  leave(id: $leaveId) {
    _id
    id
    leaveType
    reason
    startDateTime
    endDateTime
    leaveStatus
    user {
      id
      fullname
    }
    approvedBy {
      id
      fullname
    }
    rejectedReason
  }
}
    `;

export const useGetLeaveByIdQuery = <
      TData = GetLeaveByIdQuery,
      TError = Error
    >(
      variables: GetLeaveByIdQueryVariables,
      options?: Omit<UseQueryOptions<GetLeaveByIdQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<GetLeaveByIdQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<GetLeaveByIdQuery, TError, TData>(
      {
    queryKey: ['GetLeaveById', variables],
    queryFn: fetchData<GetLeaveByIdQuery, GetLeaveByIdQueryVariables>(GetLeaveByIdDocument, variables),
    ...options
  }
    )};

useGetLeaveByIdQuery.document = GetLeaveByIdDocument;

useGetLeaveByIdQuery.getKey = (variables: GetLeaveByIdQueryVariables) => ['GetLeaveById', variables];

export const LocationsDocument = `
    query Locations {
  locations {
    id
    name
    description
    address
    emergencyContact
  }
}
    `;

export const useLocationsQuery = <
      TData = LocationsQuery,
      TError = Error
    >(
      variables?: LocationsQueryVariables,
      options?: Omit<UseQueryOptions<LocationsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<LocationsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<LocationsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Locations'] : ['Locations', variables],
    queryFn: fetchData<LocationsQuery, LocationsQueryVariables>(LocationsDocument, variables),
    ...options
  }
    )};

useLocationsQuery.document = LocationsDocument;

useLocationsQuery.getKey = (variables?: LocationsQueryVariables) => variables === undefined ? ['Locations'] : ['Locations', variables];

export const CreateLocationDocument = `
    mutation CreateLocation($createLocationInput: CreateLocationInput!) {
  createLocation(createLocationInput: $createLocationInput) {
    id
  }
}
    `;

export const useCreateLocationMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateLocationMutation, TError, CreateLocationMutationVariables, TContext>) => {
    
    return useMutation<CreateLocationMutation, TError, CreateLocationMutationVariables, TContext>(
      {
    mutationKey: ['CreateLocation'],
    mutationFn: (variables?: CreateLocationMutationVariables) => fetchData<CreateLocationMutation, CreateLocationMutationVariables>(CreateLocationDocument, variables)(),
    ...options
  }
    )};

export const LocationDocument = `
    query Location($locationId: String!) {
  location(id: $locationId) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    address
    emergencyContact
  }
}
    `;

export const useLocationQuery = <
      TData = LocationQuery,
      TError = Error
    >(
      variables: LocationQueryVariables,
      options?: Omit<UseQueryOptions<LocationQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<LocationQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<LocationQuery, TError, TData>(
      {
    queryKey: ['Location', variables],
    queryFn: fetchData<LocationQuery, LocationQueryVariables>(LocationDocument, variables),
    ...options
  }
    )};

useLocationQuery.document = LocationDocument;

useLocationQuery.getKey = (variables: LocationQueryVariables) => ['Location', variables];

export const UpdateLocationDocument = `
    mutation UpdateLocation($id: ID!, $updateLocationInput: UpdateLocationInput!) {
  updateLocation(id: $id, updateLocationInput: $updateLocationInput) {
    id
  }
}
    `;

export const useUpdateLocationMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateLocationMutation, TError, UpdateLocationMutationVariables, TContext>) => {
    
    return useMutation<UpdateLocationMutation, TError, UpdateLocationMutationVariables, TContext>(
      {
    mutationKey: ['UpdateLocation'],
    mutationFn: (variables?: UpdateLocationMutationVariables) => fetchData<UpdateLocationMutation, UpdateLocationMutationVariables>(UpdateLocationDocument, variables)(),
    ...options
  }
    )};

export const ShiftDocument = `
    query Shift($input: ShiftsInput!) {
  shifts(shiftsInput: $input) {
    id
    recurringId
    startDateTime
    endDateTime
    location {
      name
      id
    }
    users {
      fullname
      id
    }
  }
}
    `;

export const useShiftQuery = <
      TData = ShiftQuery,
      TError = Error
    >(
      variables: ShiftQueryVariables,
      options?: Omit<UseQueryOptions<ShiftQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<ShiftQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<ShiftQuery, TError, TData>(
      {
    queryKey: ['Shift', variables],
    queryFn: fetchData<ShiftQuery, ShiftQueryVariables>(ShiftDocument, variables),
    ...options
  }
    )};

useShiftQuery.document = ShiftDocument;

useShiftQuery.getKey = (variables: ShiftQueryVariables) => ['Shift', variables];

export const CreateShiftDocument = `
    mutation CreateShift($input: CreateShiftInput!) {
  createShift(createShiftInput: $input)
}
    `;

export const useCreateShiftMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateShiftMutation, TError, CreateShiftMutationVariables, TContext>) => {
    
    return useMutation<CreateShiftMutation, TError, CreateShiftMutationVariables, TContext>(
      {
    mutationKey: ['CreateShift'],
    mutationFn: (variables?: CreateShiftMutationVariables) => fetchData<CreateShiftMutation, CreateShiftMutationVariables>(CreateShiftDocument, variables)(),
    ...options
  }
    )};

export const UpdateShiftDocument = `
    mutation UpdateShift($id: String!, $input: UpdateShiftInput!) {
  updateShift(id: $id, updateShiftInput: $input) {
    id
  }
}
    `;

export const useUpdateShiftMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateShiftMutation, TError, UpdateShiftMutationVariables, TContext>) => {
    
    return useMutation<UpdateShiftMutation, TError, UpdateShiftMutationVariables, TContext>(
      {
    mutationKey: ['UpdateShift'],
    mutationFn: (variables?: UpdateShiftMutationVariables) => fetchData<UpdateShiftMutation, UpdateShiftMutationVariables>(UpdateShiftDocument, variables)(),
    ...options
  }
    )};

export const DeleteShiftDocument = `
    mutation DeleteShift($id: String!) {
  removeShift(id: $id)
}
    `;

export const useDeleteShiftMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<DeleteShiftMutation, TError, DeleteShiftMutationVariables, TContext>) => {
    
    return useMutation<DeleteShiftMutation, TError, DeleteShiftMutationVariables, TContext>(
      {
    mutationKey: ['DeleteShift'],
    mutationFn: (variables?: DeleteShiftMutationVariables) => fetchData<DeleteShiftMutation, DeleteShiftMutationVariables>(DeleteShiftDocument, variables)(),
    ...options
  }
    )};

export const DeleteRecurringShiftDocument = `
    mutation DeleteRecurringShift($id: String!, $recurringId: String!) {
  removeRecurringShifts(id: $id, recurringId: $recurringId)
}
    `;

export const useDeleteRecurringShiftMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<DeleteRecurringShiftMutation, TError, DeleteRecurringShiftMutationVariables, TContext>) => {
    
    return useMutation<DeleteRecurringShiftMutation, TError, DeleteRecurringShiftMutationVariables, TContext>(
      {
    mutationKey: ['DeleteRecurringShift'],
    mutationFn: (variables?: DeleteRecurringShiftMutationVariables) => fetchData<DeleteRecurringShiftMutation, DeleteRecurringShiftMutationVariables>(DeleteRecurringShiftDocument, variables)(),
    ...options
  }
    )};

export const StorageFoldersDocument = `
    query StorageFolders($parentId: String) {
  storageFolders(parentId: $parentId) {
    id
    name
    type
    size
    path
    metadata
  }
}
    `;

export const useStorageFoldersQuery = <
      TData = StorageFoldersQuery,
      TError = Error
    >(
      variables?: StorageFoldersQueryVariables,
      options?: Omit<UseQueryOptions<StorageFoldersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<StorageFoldersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<StorageFoldersQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['StorageFolders'] : ['StorageFolders', variables],
    queryFn: fetchData<StorageFoldersQuery, StorageFoldersQueryVariables>(StorageFoldersDocument, variables),
    ...options
  }
    )};

useStorageFoldersQuery.document = StorageFoldersDocument;

useStorageFoldersQuery.getKey = (variables?: StorageFoldersQueryVariables) => variables === undefined ? ['StorageFolders'] : ['StorageFolders', variables];

export const StorageItemsDocument = `
    query StorageItems($parentId: String) {
  storageItems(parentId: $parentId) {
    id
    name
    type
    size
    path
    metadata
  }
}
    `;

export const useStorageItemsQuery = <
      TData = StorageItemsQuery,
      TError = Error
    >(
      variables?: StorageItemsQueryVariables,
      options?: Omit<UseQueryOptions<StorageItemsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<StorageItemsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<StorageItemsQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['StorageItems'] : ['StorageItems', variables],
    queryFn: fetchData<StorageItemsQuery, StorageItemsQueryVariables>(StorageItemsDocument, variables),
    ...options
  }
    )};

useStorageItemsQuery.document = StorageItemsDocument;

useStorageItemsQuery.getKey = (variables?: StorageItemsQueryVariables) => variables === undefined ? ['StorageItems'] : ['StorageItems', variables];

export const CreateFolderDocument = `
    mutation CreateFolder($createFolderInput: CreateFolderDto!) {
  createFolder(createFolderInput: $createFolderInput) {
    _id
  }
}
    `;

export const useCreateFolderMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateFolderMutation, TError, CreateFolderMutationVariables, TContext>) => {
    
    return useMutation<CreateFolderMutation, TError, CreateFolderMutationVariables, TContext>(
      {
    mutationKey: ['CreateFolder'],
    mutationFn: (variables?: CreateFolderMutationVariables) => fetchData<CreateFolderMutation, CreateFolderMutationVariables>(CreateFolderDocument, variables)(),
    ...options
  }
    )};

export const StorageSystemFoldersDocument = `
    query StorageSystemFolders {
  systemFiles {
    id
    name
    path
    parent {
      id
    }
  }
}
    `;

export const useStorageSystemFoldersQuery = <
      TData = StorageSystemFoldersQuery,
      TError = Error
    >(
      variables?: StorageSystemFoldersQueryVariables,
      options?: Omit<UseQueryOptions<StorageSystemFoldersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<StorageSystemFoldersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<StorageSystemFoldersQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['StorageSystemFolders'] : ['StorageSystemFolders', variables],
    queryFn: fetchData<StorageSystemFoldersQuery, StorageSystemFoldersQueryVariables>(StorageSystemFoldersDocument, variables),
    ...options
  }
    )};

useStorageSystemFoldersQuery.document = StorageSystemFoldersDocument;

useStorageSystemFoldersQuery.getKey = (variables?: StorageSystemFoldersQueryVariables) => variables === undefined ? ['StorageSystemFolders'] : ['StorageSystemFolders', variables];

export const CreateUserDocumentDocument = `
    mutation CreateUserDocument($input: CreateUserDocumentInput!) {
  createUserDocument(createUserDocumentInput: $input) {
    id
  }
}
    `;

export const useCreateUserDocumentMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateUserDocumentMutation, TError, CreateUserDocumentMutationVariables, TContext>) => {
    
    return useMutation<CreateUserDocumentMutation, TError, CreateUserDocumentMutationVariables, TContext>(
      {
    mutationKey: ['CreateUserDocument'],
    mutationFn: (variables?: CreateUserDocumentMutationVariables) => fetchData<CreateUserDocumentMutation, CreateUserDocumentMutationVariables>(CreateUserDocumentDocument, variables)(),
    ...options
  }
    )};

export const DeleteUserDocumentDocument = `
    mutation DeleteUserDocument($documentId: String!) {
  removeUserDocument(id: $documentId) {
    id
    documentName
  }
}
    `;

export const useDeleteUserDocumentMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<DeleteUserDocumentMutation, TError, DeleteUserDocumentMutationVariables, TContext>) => {
    
    return useMutation<DeleteUserDocumentMutation, TError, DeleteUserDocumentMutationVariables, TContext>(
      {
    mutationKey: ['DeleteUserDocument'],
    mutationFn: (variables?: DeleteUserDocumentMutationVariables) => fetchData<DeleteUserDocumentMutation, DeleteUserDocumentMutationVariables>(DeleteUserDocumentDocument, variables)(),
    ...options
  }
    )};

export const UserDocumentsDocument = `
    query UserDocuments($userDocumentFilterInput: UserDocumentFilterInput!) {
  userDocuments(filter: $userDocumentFilterInput) {
    id
    documentName
    url
  }
}
    `;

export const useUserDocumentsQuery = <
      TData = UserDocumentsQuery,
      TError = Error
    >(
      variables: UserDocumentsQueryVariables,
      options?: Omit<UseQueryOptions<UserDocumentsQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<UserDocumentsQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<UserDocumentsQuery, TError, TData>(
      {
    queryKey: ['UserDocuments', variables],
    queryFn: fetchData<UserDocumentsQuery, UserDocumentsQueryVariables>(UserDocumentsDocument, variables),
    ...options
  }
    )};

useUserDocumentsQuery.document = UserDocumentsDocument;

useUserDocumentsQuery.getKey = (variables: UserDocumentsQueryVariables) => ['UserDocuments', variables];

export const UpdateUserProfileDocument = `
    mutation UpdateUserProfile($userId: String!, $updateUserProfileInput: UpdateUserProfileInput!) {
  updateUserProfile(id: $userId, updateUserProfileInput: $updateUserProfileInput) {
    id
  }
}
    `;

export const useUpdateUserProfileMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateUserProfileMutation, TError, UpdateUserProfileMutationVariables, TContext>) => {
    
    return useMutation<UpdateUserProfileMutation, TError, UpdateUserProfileMutationVariables, TContext>(
      {
    mutationKey: ['UpdateUserProfile'],
    mutationFn: (variables?: UpdateUserProfileMutationVariables) => fetchData<UpdateUserProfileMutation, UpdateUserProfileMutationVariables>(UpdateUserProfileDocument, variables)(),
    ...options
  }
    )};

export const UserProfileDocument = `
    query UserProfile($userId: String!) {
  userProfile(id: $userId) {
    _id
    permitExpiresAt
    gender
    dob
    placeOfBirth
    currentAddress
    joinedAt
    maritalStatus
    bankAccNumber
    bankName
    emergencyContact {
      name
      relation
      contact {
        countryCode
        phone
      }
    }
    id
    ID
    ic
    passport
    passportExpiresAt
    permitNumber
  }
}
    `;

export const useUserProfileQuery = <
      TData = UserProfileQuery,
      TError = Error
    >(
      variables: UserProfileQueryVariables,
      options?: Omit<UseQueryOptions<UserProfileQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<UserProfileQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<UserProfileQuery, TError, TData>(
      {
    queryKey: ['UserProfile', variables],
    queryFn: fetchData<UserProfileQuery, UserProfileQueryVariables>(UserProfileDocument, variables),
    ...options
  }
    )};

useUserProfileQuery.document = UserProfileDocument;

useUserProfileQuery.getKey = (variables: UserProfileQueryVariables) => ['UserProfile', variables];

export const UsersDocument = `
    query Users($input: UsersInput!) {
  users(usersInput: $input) {
    id
    fullname
    phone
    userStatus
    role
  }
}
    `;

export const useUsersQuery = <
      TData = UsersQuery,
      TError = Error
    >(
      variables: UsersQueryVariables,
      options?: Omit<UseQueryOptions<UsersQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<UsersQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<UsersQuery, TError, TData>(
      {
    queryKey: ['Users', variables],
    queryFn: fetchData<UsersQuery, UsersQueryVariables>(UsersDocument, variables),
    ...options
  }
    )};

useUsersQuery.document = UsersDocument;

useUsersQuery.getKey = (variables: UsersQueryVariables) => ['Users', variables];

export const CreateUserDocument = `
    mutation CreateUser($input: CreateUserInput!) {
  signUp(input: $input) {
    access_token
  }
}
    `;

export const useCreateUserMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateUserMutation, TError, CreateUserMutationVariables, TContext>) => {
    
    return useMutation<CreateUserMutation, TError, CreateUserMutationVariables, TContext>(
      {
    mutationKey: ['CreateUser'],
    mutationFn: (variables?: CreateUserMutationVariables) => fetchData<CreateUserMutation, CreateUserMutationVariables>(CreateUserDocument, variables)(),
    ...options
  }
    )};

export const UserDocument = `
    query User($userId: String!) {
  user(id: $userId) {
    id
    fullname
    phone
    userStatus
    role
  }
}
    `;

export const useUserQuery = <
      TData = UserQuery,
      TError = Error
    >(
      variables: UserQueryVariables,
      options?: Omit<UseQueryOptions<UserQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<UserQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<UserQuery, TError, TData>(
      {
    queryKey: ['User', variables],
    queryFn: fetchData<UserQuery, UserQueryVariables>(UserDocument, variables),
    ...options
  }
    )};

useUserQuery.document = UserDocument;

useUserQuery.getKey = (variables: UserQueryVariables) => ['User', variables];

export const UpdateUserDocument = `
    mutation UpdateUser($input: UpdateUserInput!) {
  updateUser(updateUserInput: $input) {
    id
  }
}
    `;

export const useUpdateUserMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateUserMutation, TError, UpdateUserMutationVariables, TContext>) => {
    
    return useMutation<UpdateUserMutation, TError, UpdateUserMutationVariables, TContext>(
      {
    mutationKey: ['UpdateUser'],
    mutationFn: (variables?: UpdateUserMutationVariables) => fetchData<UpdateUserMutation, UpdateUserMutationVariables>(UpdateUserDocument, variables)(),
    ...options
  }
    )};

export const MeDocument = `
    query Me {
  me {
    _id
    id
    fullname
    phone
    userStatus
    role
  }
}
    `;

export const useMeQuery = <
      TData = MeQuery,
      TError = Error
    >(
      variables?: MeQueryVariables,
      options?: Omit<UseQueryOptions<MeQuery, TError, TData>, 'queryKey'> & { queryKey?: UseQueryOptions<MeQuery, TError, TData>['queryKey'] }
    ) => {
    
    return useQuery<MeQuery, TError, TData>(
      {
    queryKey: variables === undefined ? ['Me'] : ['Me', variables],
    queryFn: fetchData<MeQuery, MeQueryVariables>(MeDocument, variables),
    ...options
  }
    )};

useMeQuery.document = MeDocument;

useMeQuery.getKey = (variables?: MeQueryVariables) => variables === undefined ? ['Me'] : ['Me', variables];

export const IndexFaceDocument = `
    mutation IndexFace($indexFaceInput: IndexFaceInput!) {
  indexFace(indexFaceInput: $indexFaceInput) {
    id
    fullname
    userStatus
    role
  }
}
    `;

export const useIndexFaceMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<IndexFaceMutation, TError, IndexFaceMutationVariables, TContext>) => {
    
    return useMutation<IndexFaceMutation, TError, IndexFaceMutationVariables, TContext>(
      {
    mutationKey: ['IndexFace'],
    mutationFn: (variables?: IndexFaceMutationVariables) => fetchData<IndexFaceMutation, IndexFaceMutationVariables>(IndexFaceDocument, variables)(),
    ...options
  }
    )};

export const ClearFaceDocument = `
    mutation ClearFace($input: ClearFaceInput!) {
  clearFace(clearFaceInput: $input)
}
    `;

export const useClearFaceMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClearFaceMutation, TError, ClearFaceMutationVariables, TContext>) => {
    
    return useMutation<ClearFaceMutation, TError, ClearFaceMutationVariables, TContext>(
      {
    mutationKey: ['ClearFace'],
    mutationFn: (variables?: ClearFaceMutationVariables) => fetchData<ClearFaceMutation, ClearFaceMutationVariables>(ClearFaceDocument, variables)(),
    ...options
  }
    )};
